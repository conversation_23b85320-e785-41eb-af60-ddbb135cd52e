# VSCode 设置应用指南

## 🚀 快速应用方法

### 方法一：使用批处理脚本（推荐）
1. 双击运行 `应用VSCode设置.bat`
2. 脚本会自动备份原设置并应用新设置
3. 重启VSCode查看效果

### 方法二：手动应用
1. 打开VSCode
2. 按 `Ctrl + Shift + P` 打开命令面板
3. 输入 `Preferences: Open Settings (JSON)`
4. 打开 `优化的VSCode设置.json` 文件
5. 复制所有内容到VSCode设置文件中
6. 按 `Ctrl + S` 保存
7. 重启VSCode

## 🎨 主要优化内容

### 字体和显示
- ✅ 优化了中文字体显示（Microsoft YaHei）
- ✅ 调整了字体大小为15px，行高1.6
- ✅ 启用了平滑滚动和光标动画
- ✅ 优化了代码高亮和选择效果

### Lua开发体验
- ✅ 配置了完整的Lua全局变量列表
- ✅ 包含了你脚本中的所有中文函数名
- ✅ 优化了代码提示和补全
- ✅ 禁用了不必要的警告

### 界面优化
- ✅ 启用了彩色括号匹配
- ✅ 显示缩进参考线
- ✅ 优化了小地图显示
- ✅ 改善了文件资源管理器

### 编辑器行为
- ✅ 设置Tab大小为4，使用Tab字符
- ✅ 启用自动换行，设置标尺线
- ✅ 优化了代码折叠和格式化
- ✅ 配置了自动保存

## 🔧 推荐安装的插件

由于命令行安装插件可能有问题，建议手动安装以下插件：

### 必装插件（在VSCode扩展面板搜索安装）
1. **Chinese (Simplified)** - 中文界面（如果需要）
2. **Bracket Pair Colorizer 2** - 彩色括号
3. **indent-rainbow** - 彩色缩进线
4. **Better Comments** - 增强注释

### 可选插件
1. **One Dark Pro** - 更好的深色主题
2. **Material Icon Theme** - 美观的文件图标
3. **Code Runner** - 一键运行代码
4. **GitLens** - Git增强工具

## 📝 安装插件步骤

1. 打开VSCode
2. 按 `Ctrl + Shift + X` 打开扩展面板
3. 在搜索框中输入插件名称
4. 点击 "Install" 安装
5. 安装完成后重启VSCode

## 🎯 针对你的游戏脚本的特殊配置

设置文件已经包含了：

### Lua全局变量配置
```json
"lua.diagnostics.globals": [
    "app", "sleep", "tap", "toast", "findMultiColor",
    "arr", "pkgname", "显示", "多点找色", "加速",
    "争霸坐标表", "杀戮小地图坐标表", "神秘移动坐标表",
    // ... 还有更多你脚本中使用的变量
]
```

### 文件关联
```json
"files.associations": {
    "*.lua": "lua",
    "*.luaej": "lua",
    "*.luacheckrc": "lua"
}
```

### 中文支持
```json
"files.encoding": "utf8",
"files.autoGuessEncoding": true
```

## 🔍 验证设置是否生效

应用设置后，你应该看到：
1. 字体变得更清晰，中文显示正常
2. 代码有了更好的高亮效果
3. 括号有了颜色区分（如果安装了插件）
4. 缩进线更清晰
5. Lua代码提示更准确，没有红色波浪线错误

## ❓ 常见问题

### Q: 设置没有生效？
A: 确保重启了VSCode，有些设置需要重启才能生效

### Q: 中文显示有问题？
A: 检查字体设置，确保包含了 'Microsoft YaHei'

### Q: 代码提示不准确？
A: 确保Lua Language Server插件已安装并启用

### Q: 想恢复原设置？
A: 使用备份文件 `settings.json.backup.日期` 恢复

## 🎉 享受优化后的编码体验！

应用这些设置后，你的VSCode界面会变得更加美观和实用，特别是对于Lua游戏脚本开发会有很大的改善！
