{
    // ==================== 字体和外观设置 ====================
    "editor.fontFamily": "'Consolas', 'Microsoft YaHei', 'Courier New', monospace",
    "editor.fontSize": 15,
    "editor.lineHeight": 1.6,
    "editor.fontWeight": "normal",
    // ==================== 主题和颜色（护眼配置） ====================
    "workbench.colorTheme": "Quiet Light",
    "workbench.preferredDarkColorTheme": "Visual Studio Dark",
    "workbench.preferredLightColorTheme": "Quiet Light",
    // ==================== 编辑器行为优化 ====================
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    "editor.detectIndentation": true,
    "editor.wordWrap": "on",
    "editor.wordWrapColumn": 120,
    "editor.rulers": [
        80,
        120
    ],
    "editor.autoIndent": "full",
    // ==================== 视觉效果增强 ====================
    "editor.renderWhitespace": "boundary",
    "editor.renderIndentGuides": true,
    "editor.renderLineHighlight": "all",
    "editor.cursorBlinking": "smooth",
    "editor.cursorSmoothCaretAnimation": "on",
    "editor.smoothScrolling": true,
    "editor.mouseWheelZoom": true,
    // ==================== 行号和代码折叠 ====================
    "editor.lineNumbers": "on",
    "editor.folding": true,
    "editor.foldingStrategy": "indentation",
    "editor.showFoldingControls": "mouseover",
    "editor.foldingHighlight": true,
    "editor.foldingImportsByDefault": false,
    // ==================== 括号匹配和高亮（柔和配置） ====================
    "editor.bracketPairColorization.enabled": false, // 关闭彩色括号，减少视觉干扰
    "editor.guides.bracketPairs": "never",
    "editor.matchBrackets": "always",
    "editor.autoClosingBrackets": "always",
    "editor.autoClosingQuotes": "always",
    "editor.autoSurround": "languageDefined",
    // ==================== 选择和搜索高亮（柔和配置） ====================
    "editor.selectionHighlight": true,
    "editor.occurrencesHighlight": true,
    "editor.findMatchHighlight": true,
    "editor.wordHighlightBackground": "#e6f3ff80", // 浅蓝色，更柔和
    "editor.wordHighlightStrongBackground": "#cce7ff80", // 更浅的蓝色
    // ==================== 代码提示和智能感知 ====================
    "editor.suggestSelection": "first",
    "editor.acceptSuggestionOnCommitCharacter": false,
    "editor.acceptSuggestionOnEnter": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    },
    "editor.parameterHints.enabled": true,
    "editor.hover.enabled": true,
    "editor.hover.delay": 300,
    // ==================== 格式化设置 ====================
    "editor.formatOnSave": true,
    "editor.formatOnType": false,
    "editor.formatOnPaste": true,
    "editor.trimAutoWhitespace": true,
    // ==================== Lua语言特定设置 ====================
    "[lua]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": false,
        "editor.wordWrap": "on",
        "editor.rulers": [
            80,
            120
        ],
        "editor.defaultFormatter": "sumneko.lua",
        "editor.semanticHighlighting.enabled": true
    },
    // ==================== 文件关联 ====================
    "files.associations": {
        "*.lua": "lua",
        "*.luaej": "lua",
        "*.luacheckrc": "lua"
    },
    "files.encoding": "utf8",
    "files.autoGuessEncoding": true,
    // ==================== Lua Language Server配置 ====================
    "lua.diagnostics.globals": [
        "app",
        "sleep",
        "tap",
        "toast",
        "findMultiColor",
        "getPixelColor",
        "swipe",
        "runApp",
        "appIsFront",
        "tickCount",
        "print",
        "math",
        "os",
        "string",
        "table",
        "require",
        "pcall",
        "xpcall",
        "pairs",
        "ipairs",
        "arr",
        "pkgname",
        "显示",
        "多点找色",
        "多点找色争霸",
        "多点找色世界",
        "多点找色阶",
        "多点找色星",
        "多点找色神秘",
        "多点找色通天",
        "多点找色重叠",
        "各类弹窗",
        "加速",
        "瞬移",
        "通用进入战斗",
        "争霸传送",
        "世界传送",
        "星传送",
        "杀戮爬楼",
        "华山",
        "神秘",
        "分解封装",
        "闪退重启",
        "颜色检查",
        "争霸坐标表",
        "杀戮小地图坐标表",
        "神秘移动坐标表",
        "挂星小地图坐标表",
        "世界勾选层数",
        "当前索引",
        "分解时间",
        "开始丢丹",
        "当前加速值",
        "上次加速更新时间",
        "怪物",
        "世界boss怪物",
        "星怪",
        "杀戮战场阶",
        "神秘boss",
        "卡地图挂星",
        "卡地图世界",
        "卡地图争霸",
        "卡地图杀戮",
        "挂星击杀够换图",
        "世界击杀满等",
        "脚本开始函数",
        "判断分辨率",
        "恶意杀戮提醒",
        "同屏人数",
        "setControlBarPosNew"
    ],
    "lua.diagnostics.disable": [
        "lowercase-global",
        "undefined-global",
        "unused-local",
        "unused-vararg"
    ],
    "lua.completion.enable": true,
    "lua.completion.callSnippet": "Both",
    "lua.completion.keywordSnippet": "Both",
    "lua.hover.enable": true,
    "lua.signatureHelp.enable": true,
    "lua.workspace.checkThirdParty": false,
    // ==================== 工作区和界面布局 ====================
    "workbench.sideBar.location": "left",
    "workbench.activityBar.visible": true,
    "workbench.statusBar.visible": true,
    "workbench.editor.showTabs": true,
    "workbench.editor.tabCloseButton": "right",
    "workbench.editor.tabSizing": "fit",
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "welcomePage",
    // ==================== 终端设置 ====================
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.fontFamily": "'Consolas', 'Microsoft YaHei', monospace",
    "terminal.integrated.cursorBlinking": true,
    "terminal.integrated.cursorStyle": "line",
    "terminal.integrated.scrollback": 10000,
    // ==================== 文件资源管理器 ====================
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "explorer.compactFolders": false,
    "explorer.sortOrder": "type",
    // ==================== 小地图设置 ====================
    "editor.minimap.enabled": true,
    "editor.minimap.side": "right",
    "editor.minimap.showSlider": "mouseover",
    "editor.minimap.renderCharacters": false,
    "editor.minimap.maxColumn": 120,
    // ==================== 面包屑导航 ====================
    "breadcrumbs.enabled": true,
    "breadcrumbs.showFiles": true,
    "breadcrumbs.showSymbols": true,
    // ==================== 问题面板 ====================
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    // ==================== 搜索设置 ====================
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/tmp": true,
        "**/.git": true
    },
    "search.useIgnoreFiles": true,
    "search.smartCase": true,
    // ==================== 自动保存 ====================
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 2000,
    "files.hotExit": "onExitAndWindowClose",
    // ==================== Git设置 ====================
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    "git.decorations.enabled": true,
    // ==================== 性能优化 ====================
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/tmp/**": true
    },
    "extensions.autoUpdate": false,
    // ==================== 其他优化设置 ====================
    "workbench.list.smoothScrolling": true,
    "editor.accessibilitySupport": "off",
    "editor.find.addExtraSpaceOnTop": false,
    "diffEditor.ignoreTrimWhitespace": false,
    "editor.emptySelectionClipboard": false
}