


function 获取顺序坐标()
	-- 获取当前索引的坐标
	local 坐标 = 神秘移动坐标表[当前索引]
	
	-- 更新索引，如果到达末尾，则重置为第一个
	当前索引 = 当前索引 + 1
	if 当前索引 > #神秘移动坐标表 then
		当前索引 = 1
	end
	
	return 坐标
end

-- 示例：获取不同的随机坐标


function 颜色检查(colorList, tolerance, interval)
	for _, color in ipairs(colorList) do
		if 多点找色(color, tolerance, interval) then
			return true
		end
	end
	return false
end

local function get丹颜色()
	local 丹颜色 = {}
	
	if arr.page1["500丹"] == "true" then table.insert(丹颜色, 五百丹) end
	if arr.page1["1000丹"] == "true" then table.insert(丹颜色, 一千丹) end
	if arr.page1["1500丹"] == "true" then table.insert(丹颜色,一千五丹) end
	if arr.page1["2000丹"] == "true" then table.insert(丹颜色, 二千丹) end
	if arr.page1["2500丹"] == "true" then table.insert(丹颜色, 二千五丹) end
	if arr.page1["3000丹"] == "true" then table.insert(丹颜色, 三千丹) end
	if arr.page1["3500丹"] == "true" then table.insert(丹颜色, 三千五丹) end
	if arr.page1["4000丹"] == "true" then table.insert(丹颜色, 四千丹) end
	if arr.page1["4500丹"] == "true" then table.insert(丹颜色, 四千五丹) end
	if arr.page1["5000丹"] == "true" then table.insert(丹颜色, 五千丹) end
	if arr.page1["5500丹"] == "true" then table.insert(丹颜色, 五千五丹) end
	--[===[   if arr.page1["6000以上丹"] == "true" then table.insert(丹颜色, 六千以上丹) end]===]
	
	return 丹颜色
end

-- 修改丢丹啊函数
function 丢丹()
	if 多点找色(背包, 1, 1000) then
		多点找色(一键整理, 1, 500)
		for h = 1, 4 do
			sleep(100)
			swipe(983, 256, 986, 530, 300) -- 向上滑
		end
		tap(851, 161)
		sleep(500)
	end
	
	if 多点找色(背包面板, 0, 0) then
		local 丹颜色 = get丹颜色() -- 使用动态获取的丹药颜色列表
		for i = 1, 15 do
			sleep(100)
			--多点找色(世界之王确定,1,300)
			if 颜色检查(丹颜色, 1, 300)  then
				多点找色(更多, 1, 400)
				多点找色(丢弃, 1, 400)
				多点找色(丢弃确定, 1, 400)
			elseif 多点找色(杀戮牌子五层,1,300) then
				多点找色(杀戮丢弃, 1, 400)
				多点找色(丢弃确定, 1, 400)
			end
		end
		
		if not 颜色检查(丹颜色, 0, 0) and not 多点找色(杀戮牌子五层, 0, 0) then
			for i = 1, 4 do
				sleep(300)
				--print(i)
				if 多点找色(背包面板, 0, 0) then
					-- print("该滑动了")
					swipe(892, 577, 894, 300, 600)
					for j = 1, 15 do
						sleep(100)
						--多点找色(世界之王确定,1,300)
						if 颜色检查(丹颜色, 1, 300) then
							多点找色(更多, 1, 400)
							多点找色(丢弃, 1, 400)
							多点找色(丢弃确定, 1, 400)
						elseif 多点找色(杀戮牌子五层,1,300) then
							多点找色(杀戮丢弃, 1, 400)
							多点找色(丢弃确定, 1, 400)
						end
					end
				end
			end
			显示("没了")
			多点找色(背包叉, 1, 1000)
			return
		end
	end
end


function 多点找色争霸(数据,点击,时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end
		local xm,ym = findMultiColor(数据[1],数据[2],数据[3],数据[4],数据[5],数据[6],1,0.9)
		if xm>-1 then
			
			if 点击==1 then
				tap(xm-37,ym-37)
				
				sleep(时间)
				
				
			end
			return true
		else
			return false
			
		end
	end)
	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$")  -- 提取文件名
		local 错误信息 = string.format("多点找色错误: %s 第%d行 - %s", 文件名, 信息.currentline, 结果)
		-- 写入错误日志（如果函数存在的话）
		if 写入错误日志 then
			写入错误日志(错误信息)
		else
			print(错误信息)
		end
		return false
	end
	return 结果
end

function 多点找色星(数据, 点击, 时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end
		local x, y = findMultiColor(数据[1], 数据[2], 数据[3], 数据[4], 数据[5], 数据[6], 1, 0.9)
		if x > -1 then
			if 点击 == 1 then
				tap(x,y-33)
				sleep(时间)
			end
			return true
		else
			return false
		end
	end)
	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$")  -- 提取文件名
		local 错误信息 = string.format("多点找色错误: %s 第%d行 - %s", 文件名, 信息.currentline, 结果)
		-- 写入错误日志（如果函数存在的话）
		if 写入错误日志 then
			写入错误日志(错误信息)
		else
			print(错误信息)
		end
		return false
	end
	return 结果
end

function 多点找色世界(数据,点击,时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end
		local x,y = findMultiColor(数据[1],数据[2],数据[3],数据[4],数据[5],数据[6],1,0.9)
		if x>-1 then
			
			if 点击==1 then
				tap(x-20,y-60)
				
				sleep(时间)
				
				
			end
			return true
		else
			return false
		end
	end)
	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$")  -- 提取文件名
		local 错误信息 = string.format("多点找色错误: %s 第%d行 - %s", 文件名, 信息.currentline, 结果)
		-- 写入错误日志（如果函数存在的话）
		if 写入错误日志 then
			写入错误日志(错误信息)
		else
			print(错误信息)
		end
		return false
	end
	return 结果
end
function 多点找色阶(数据,点击,时间)
	
	local xj,yj = findMultiColor(数据[1],数据[2],数据[3],数据[4],数据[5],数据[6],1,0.9)
	if xj>-1 then
		
		if 点击==1 then
			tap(xj-21,yj-50)
			
			sleep(时间)
			
			
		end
		return true
	else
		return false
		
	end
	
end



function 多点找色(数据, 点击, 时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end
		local x, y = findMultiColor(数据[1], 数据[2], 数据[3], 数据[4], 数据[5], 数据[6], 0, 0.9)
		if x > -1 then
			if 点击 == 1 then
				tap(x,y)
				sleep(时间)
			end
			return true
		else
			return false
		end
	end)
	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$")  -- 提取文件名
		local 错误信息 = string.format("多点找色错误: %s 第%d行 - %s", 文件名, 信息.currentline, 结果)
		-- 写入错误日志（如果函数存在的话）
		if 写入错误日志 then
			写入错误日志(错误信息)
		else
			print(错误信息)
		end
		return false
	end
	return 结果
end
function 多点找色重叠(数据, 点击, 时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end
		local x, y = findMultiColor(数据[1], 数据[2], 数据[3], 数据[4], 数据[5], 数据[6], 2, 0.9)
		if x > -1 then
			if 点击 == 1 then
				tap(x, y)
				sleep(时间)
			end
			return true
		else
			return false
		end
	end)
	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$")  -- 提取文件名
		local 错误信息 = string.format("多点找色错误: %s 第%d行 - %s", 文件名, 信息.currentline, 结果)
		-- 写入错误日志（如果函数存在的话）
		if 写入错误日志 then
			写入错误日志(错误信息)
		else
			print(错误信息)
		end
		return false
	end
	return 结果
end

function 显示(内容)
	--print(内容)
	toast (内容,35,645,12)
end
function 多点找色神秘(数据,点击,时间)
	
	local xsm,ysm = findMultiColor(数据[1],数据[2],数据[3],数据[4],数据[5],数据[6],1,0.9)
	if xsm>-1 then
		
		if 点击==1 then
			tap(xsm,ysm-30)
			
			sleep(时间)
			
			
		end
		return true
	else
		return false
		
	end
	
end
function 多点找色通天(数据,点击,时间)
	
	local xtt,ytt = findMultiColor(数据[1],数据[2],数据[3],数据[4],数据[5],数据[6],2,0.9)
	if xtt>-1 then
		
		if 点击==1 then
			tap(xtt,ytt-30)
			
			sleep(时间)
			
			
		end
		return true
	else
		return false
		
	end
	
end
function 各类弹窗()
	local 随机索引 = math.random(1, #点到游戏图标移动坐标表)
	local 随机坐标 = 失败坐标表[随机索引]
	local 随机索引图标 = math.random(1, #点到游戏图标移动坐标表)
	local 随机坐标图标 = 点到游戏图标移动坐标表[随机索引图标]
	闪退重启()
	if 多点找色(失败,0,0)then
		
		随机索引杀戮 = math.random(1, #失败坐标表)
		随机坐标杀戮 = 失败坐标表[随机索引杀戮]
		tap(随机坐标杀戮[1], 随机坐标杀戮[2])
		sleep(200)
		print("弹窗")
	elseif 多点找色(任务栏,1,1000)then
		显示("关闭任务栏")
	elseif 多点找色(只点拒绝,1,200) then
		print("拒绝")
	elseif 多点找色(小地图叉,1,400) then
		
		
	elseif 多点找色(奖励,0,0) then
		多点找色(奖励叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
	elseif 多点找色(酒馆面板,0,0) then
		多点找色(酒馆老板叉,1,300)
		print("弹窗")
	elseif 多点找色(杀戮小地图叉,1,300) then
		print("弹窗")
	elseif 多点找色(争霸小地图叉,1,1000) then
		print("弹窗")
	elseif 多点找色(商会,0,0) then
		多点找色(商会叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
	elseif 多点找色(充值,0,0) then
		多点找色(充值叉,1,500)
		print("弹窗")
		
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
	elseif 多点找色(捐献,0,0) then
		多点找色(捐献叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
	elseif 多点找色(活动,0,0)then
		多点找色(活动叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(200)
		print("弹窗")
		
	elseif 多点找色(高级,0,0) then
		多点找色(高级回收叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
		
	elseif 多点找色(最强王者叉,1,500) then
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(200)
		print("弹窗")
		
	elseif 多点找色(队友头像,0,0) then
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
	elseif 多点找色(快捷功能叉,1,300) then
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(200)
		print("弹窗")
	elseif 多点找色(星传送叉,1,300) then
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(200)
		print("弹窗")
		
	elseif 多点找色(打沙包,0,0) then
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
		
	elseif 多点找色(对话栏,1,500) then
		print("弹窗")
		
	elseif 多点找色(宝石合成叉,1,500) then
		print("弹窗")
		
		
		--print("1")
		
		--[===[elseif 多点找色(人物叠加,1,1000)then
		print("弹窗")
		]===]
		
		--elseif 多点找色(称号弹窗,1,1000)then
	elseif 多点找色(临时背包,0,0)then
		多点找色(临时背包叉,1,500)
		tap(随机坐标图标[1], 随机坐标图标[2])
		sleep(500)
		print("弹窗")
		
	elseif 多点找色(丢弃吗, 0, 0) then
		多点找色(丢弃确定, 1, 500)
		print("弹窗")
	elseif 多点找色(杀戮丢弃吗, 0, 0) then
		多点找色(丢弃确定, 1, 500)			--print("1")
		print("弹窗")
	end
end

function 世界击杀满等()
	if 多点找色(战斗场, 0, 0) == false and (多点找色(击杀满, 0, 0) or 多点找色(击杀满二) or 多点找色(击杀满三)) then
		显示("本层已击杀满")
		多点找色(只点拒绝, 1, 200)
		
		世界boss层数 = 世界boss层数 + 1
		显示("当前世界boss层数" .. 世界boss层数)
		
		if 世界boss层数 >= 世界勾选层数 and 多点找色(战斗场, 0, 0) == false then
			多点找色(只点拒绝, 1, 500)
			显示("已经打够层数")
			世界可打 = 1
		end
		
		return true
	end
	return false
end




-- 挂星地图配置表
local 挂星地图配置 = {
{name = "一星", x = 196, y = 154, dt = "一星"},
{name = "二星", x = 416, y = 150, dt = "二星"},
{name = "三星", x = 634, y = 154, dt = "三星"},
{name = "四星", x = 853, y = 154, dt = "四星"},
{name = "五星", x = 1078, y = 153, dt = "五星"},
{name = "六星", x = 191, y = 214, dt = "六星", 传送函数 = 星传送, 找怪函数 = function() return 多点找色阶(杀戮战场阶, 1, 200) end},
{name = "七星", x = 412, y = 211, dt = "七星", 传送函数 = 星传送, 找怪函数 = function() return 多点找色阶(杀戮战场阶, 1, 200) end},
{name = "八星", x = 636, y = 211, dt = "八星"},
{name = "九星", x = 858, y = 212, dt = "九星"},
{name = "十星", x = 1067, y = 211, dt = "十星"},
{name = "十一星", x = 195, y = 273, dt = "十一星"},
{name = "十二星", x = 408, y = 273, dt = "十二星"},
{name = "十三星", x = 636, y = 274, dt = "十三星"},
{name = "十四星", x = 855, y = 274, dt = "十四星", 传送函数 = 星传送, 找怪函数 = function() return 多点找色阶(杀戮战场阶, 1, 200) end},
{name = "十五星", x = 1067, y = 272, dt = "十五星"},
{name = "十六星", x = 189, y = 335, dt = "十六星"},
{name = "十七星", x = 410, y = 332, dt = "十七星"},
{name = "十八星", x = 624, y = 336, dt = "十八星"},
{name = "十九星", x = 853, y = 334, dt = "十九星"},
{name = "二十星", x = 1073, y = 331, dt = "二十星"},
{name = "二十一星", x = 189, y = 392, dt = "去二十一星"},
{name = "二十二星", x = 413, y = 394, dt = "去二十二星"},
{name = "二十三星", x = 627, y = 395, dt = "去二十三星"},
{name = "二十四星", x = 858, y = 392, dt = "去二十四星"},
{name = "二十五星", x = 1067, y = 395, dt = "去二十五星"},
{name = "二十六星", x = 191, y = 454, dt = "去二十六星"},
{name = "二十七星", x = 406, y = 449, dt = "去二十七星"},
{name = "二十八星", x = 624, y = 457, dt = "去二十八星"},
{name = "二十九星", x = 854, y = 454, dt = "去二十九星"},
{name = "三十星", x = 1078, y = 455, dt = "去三十星"},
{name = "三十一星", x = 190, y = 515, dt = "去三十一星"},
{name = "三十二星", x = 417, y = 514, dt = "去三十二星"},
{name = "三十三星", x = 628, y = 512, dt = "去三十三星"},
{name = "三十四星", x = 857, y = 513, dt = "去三十四星"},
{name = "三十五星", x = 1070, y = 517, dt = "去三十五星"},
{name = "三十六星", x = 186, y = 575, dt = "去三十六星"},
{name = "三十七星", x = 414, y = 574, dt = "去三十七星"},
{name = "三十八星", x = 634, y = 576, dt = "去三十八星"},
{name = "三十九星", x = 858, y = 576, dt = "去三十九星"},
{name = "四十星", x = 1049, y = 574, dt = "去四十星"},
{name = "四十一星", x = 189, y = 633, dt = "去四十一星"},
{name = "四十二星", x = 401, y = 631, dt = "去四十二星"},
{name = "四十三星", x = 631, y = 633, dt = "去四十三星"},
{name = "四十四星", x = 845, y = 634, dt = "去四十四星"},
{name = "四十五星", x = 1052, y = 636, dt = "去四十五星"},
{name = "四十六星", x = 195, y = 616, dt = "去四十六星", 传送函数 = "四十五挂星传送"},
{name = "四十七星", x = 408, y = 614, dt = "去四十七星", 传送函数 = "四十五挂星传送"},
{name = "四十八星", x = 628, y = 612, dt = "去四十八星", 传送函数 = "四十五挂星传送"},
{name = "四十九星", x = 852, y = 612, dt = "去四十九星", 传送函数 = "四十五挂星传送"},
{name = "五十星", x = 1064, y = 614, dt = "去五十星", 传送函数 = "四十五挂星传送"},
{name = "五十一星", x = 186, y = 105, dt = "去五十一星", 传送函数 = "五十六挂星传送"},
{name = "五十二星", x = 413, y = 110, dt = "去五十二星", 传送函数 = "五十六挂星传送"},
{name = "五十三星", x = 638, y = 107, dt = "去五十三星", 传送函数 = "五十六挂星传送"},
{name = "五十四星", x = 854, y = 107, dt = "去五十四星", 传送函数 = "五十六挂星传送"},
{name = "五十五星", x = 1050, y = 114, dt = "去五十五星", 传送函数 = "五十六挂星传送"},
{name = "五十六星", x = 196, y = 166, dt = "去五十六星", 传送函数 = "五十六挂星传送"},
{name = "五十七星", x = 402, y = 163, dt = "去五十七星", 传送函数 = "五十六挂星传送"},
{name = "五十八星", x = 631, y = 166, dt = "去五十八星", 传送函数 = "五十六挂星传送"},
{name = "五十九星", x = 846, y = 165, dt = "去五十九星", 传送函数 = "五十六挂星传送"},
{name = "六十星", x = 1056, y = 165, dt = "去六十星", 传送函数 = "五十六挂星传送"},
{name = "六十一星", x = 187, y = 225, dt = "去六十一星", 传送函数 = "五十六挂星传送"},
{name = "六十二星", x = 404, y = 228, dt = "去六十二星", 传送函数 = "五十六挂星传送"},
{name = "六十三星", x = 625, y = 226, dt = "去六十三星", 传送函数 = "五十六挂星传送"},
{name = "六十四星", x = 850, y = 226, dt = "去六十四星", 传送函数 = "五十六挂星传送"},
{name = "六十五星", x = 1072, y = 222, dt = "去六十五星", 传送函数 = "五十六挂星传送"},
{name = "六十六星", x = 185, y = 285, dt = "去六十六星", 传送函数 = "五十六挂星传送"},
{name = "六十七星", x = 410, y = 285, dt = "去六十七星", 传送函数 = "五十六挂星传送"},
{name = "六十八星", x = 629, y = 284, dt = "去六十八星", 传送函数 = "五十六挂星传送"},
{name = "六十九星", x = 855, y = 284, dt = "去六十九星", 传送函数 = "五十六挂星传送"},
{name = "七十星", x = 1065, y = 287, dt = "去七十星", 传送函数 = "五十六挂星传送"},
{name = "七十一星", x = 187, y = 347, dt = "去七十一星", 传送函数 = "五十六挂星传送"},
{name = "七十二星", x = 407, y = 344, dt = "去七十二星", 传送函数 = "五十六挂星传送"},
{name = "七十三星", x = 636, y = 344, dt = "去七十三星", 传送函数 = "五十六挂星传送"},
{name = "七十四星", x = 854, y = 345, dt = "去七十四星", 传送函数 = "五十六挂星传送"},
{name = "七十五星", x = 1079, y = 342, dt = "去七十五星", 传送函数 = "五十六挂星传送"},
{name = "七十六星", x = 191, y = 405, dt = "去七十六星", 传送函数 = "五十六挂星传送"},
{name = "七十七星", x = 413, y = 407, dt = "去七十七星", 传送函数 = "五十六挂星传送"},
{name = "七十八星", x = 632, y = 403, dt = "去七十八星", 传送函数 = "五十六挂星传送"},
{name = "七十九星", x = 851, y = 406, dt = "去七十九星", 传送函数 = "五十六挂星传送"},
{name = "八十星", x = 1071, y = 406, dt = "去八十星", 传送函数 = "五十六挂星传送"},
{name = "八十一星", x = 194, y = 467, dt = "去八十一星", 传送函数 = "五十六挂星传送"},
{name = "八十二星", x = 405, y = 466, dt = "去八十二星", 传送函数 = "五十六挂星传送"},
{name = "八十三星", x = 630, y = 468, dt = "去八十三星", 传送函数 = "五十六挂星传送"},
{name = "八十四星", x = 850, y = 465, dt = "去八十四星", 传送函数 = "五十六挂星传送"},
{name = "八十五星", x = 1078, y = 465, dt = "去八十五星", 传送函数 = "五十六挂星传送"},
{name = "八十六星", x = 194, y = 527, dt = "去八十六星", 传送函数 = "五十六挂星传送"},
{name = "八十七星", x = 406, y = 524, dt = "去八十七星", 传送函数 = "五十六挂星传送"},
{name = "八十八星", x = 628, y = 524, dt = "去八十八星", 传送函数 = "五十六挂星传送"},
{name = "八十九星", x = 852, y = 527, dt = "去八十九星", 传送函数 = "五十六挂星传送"},
{name = "九十星", x = 1064, y = 523, dt = "去九十星", 传送函数 = "五十六挂星传送"},
{name = "九十一星", x = 188, y = 584, dt = "去九十一星", 传送函数 = "五十六挂星传送"},
{name = "九十二星", x = 409, y = 584, dt = "去九十二星", 传送函数 = "五十六挂星传送"},
{name = "九十三星", x = 625, y = 586, dt = "去九十三星", 传送函数 = "五十六挂星传送"},
{name = "九十四星", x = 847, y = 587, dt = "去九十四星", 传送函数 = "五十六挂星传送"},
{name = "九十五星", x = 1050, y = 583, dt = "去九十五星", 传送函数 = "五十六挂星传送"},
{name = "九十六星", x = 193, y = 645, dt = "去九十六星", 传送函数 = "五十六挂星传送"},
{name = "九十七星", x = 414, y = 643, dt = "去九十七星", 传送函数 = "五十六挂星传送"},
{name = "九十八星", x = 629, y = 643, dt = "去九十八星", 传送函数 = "五十六挂星传送"},
{name = "九十九星", x = 849, y = 642, dt = "去九十九星", 传送函数 = "五十六挂星传送"},
{name = "一百星", x = 1069, y = 643, dt = "去一百星", 传送函数 = "五十六挂星传送"}
}

-- 统一的挂星执行函数
function 执行挂星地图(地图配置)
	if arr["page3"][地图配置.name] == "true" then
		-- 根据字符串获取实际的传送函数
		local 实际传送函数
		if 地图配置.传送函数 == "五十六挂星传送" then
			实际传送函数 = 五十六挂星传送
			--显示("执行" .. 地图配置.name .. "，使用五十六挂星传送")
		elseif 地图配置.传送函数 == "四十五挂星传送" then
			实际传送函数 = 四十五挂星传送
			显示("执行" .. 地图配置.name .. "，使用四十五挂星传送")
		else
			实际传送函数 = 星传送
			--显示("执行" .. 地图配置.name .. "，使用星传送")
		end
		
		-- 统一调用格式：(x, y, dt, 传送函数, 找怪函数)
		挂星地图通用(地图配置.x, 地图配置.y, 地图配置.dt, 实际传送函数, 地图配置.找怪函数)
	end
end

-- 重构后的挂星图函数
function 挂星图()
	-- 根据用户选择决定执行顺序
	if arr["page1"]["挂星由高到低打"] == "true" then
		-- 从高到低执行（100星到1星）
		for i = #挂星地图配置, 1, -1 do
			执行挂星地图(挂星地图配置[i])
		end
	else
		-- 从低到高执行（1星到100星）
		for i = 1, #挂星地图配置 do
			执行挂星地图(挂星地图配置[i])
		end
	end
end

-- 挂星倒函数已合并到挂星图函数中
function 世界()
	世界boss地图("多选框41",182,512,普通世界传送)
	世界boss地图("多选框42",393,513,普通世界传送)
	世界boss地图("多选框43",609,516,普通世界传送)
	世界boss地图("多选框44",836,515,普通世界传送)
	世界boss地图("多选框45",1050,511,普通世界传送)
	世界boss地图("多选框46",178,571,普通世界传送)
	世界boss地图("多选框47",404,572,普通世界传送)
	世界boss地图("多选框48",830,574,普通世界传送)
	世界boss地图("多选框49",856,477,普通世界传送)
	世界boss地图("多选框50",1058,571,普通世界传送)
	世界boss地图("多选框51",184,632,普通世界传送)
	世界boss地图("多选框52",404,631,普通世界传送)
	世界boss地图("多选框53",615,631,普通世界传送)
	世界boss地图("多选框54",831,632,普通世界传送)
	世界boss地图("多选框55",1040,630,普通世界传送)
	世界boss地图("多选框56",163,594,世界boss滑动传送)---从这里开始
	世界boss地图("多选框57",384,591,世界boss滑动传送)
	世界boss地图("多选框58",614,591,世界boss滑动传送)
	世界boss地图("多选框59",836,589,世界boss滑动传送)
	世界boss地图("多选框60",1046,590,世界boss滑动传送)
end
function 争霸boss()
	争霸("多选框1", 185,214, "前往500级争霸")
	争霸("多选框2", 407,212, "前往1000级争霸")
	争霸("多选框3", 634,214, "前往1500级争霸")
	争霸("多选框4", 861,214, "前往2000级争霸")
	争霸("多选框5", 1075,214, "前往2500级争霸")
	争霸("多选框6", 179,270, "前往3000级争霸")
	争霸("多选框7", 403,274, "前往3500级争霸")
	争霸("多选框8", 640,272, "前往4000级争霸")
	争霸("多选框9", 854,269, "前往4500级争霸")
	争霸("多选框10", 1069,273, "前往5000级争霸")
	争霸("多选框11", 185,332, "前往5500级争霸")
	争霸("多选框12", 419,334, "前往6000级争霸")
	争霸("多选框13", 637,328, "前往6500级争霸")
	争霸("多选框14", 845,337, "前往7000级争霸")
	争霸("多选框15", 1072,335, "前往7500级争霸")
	争霸("多选框16", 182,392, "前往8000级争霸")
	争霸("多选框17", 425,389, "前往8500级争霸")
	争霸("多选框18", 633,395, "前往9000级争霸")
	争霸("多选框19", 845,392, "前往9500级争霸")
	争霸("多选框20", 1078,392, "前往9999级争霸")
end

function 杀戮()
	杀戮战场地图("多选框21",1)
	杀戮战场地图("多选框22",2)
	杀戮战场地图("多选框23",3)
	杀戮战场地图("多选框24",4)
	杀戮战场地图("多选框25",5)
	杀戮战场地图("多选框26",6)
	杀戮战场地图("多选框27",7)
	杀戮战场地图("多选框28",8)
	杀戮战场地图("多选框29",9)
	杀戮战场地图("多选框30",10)
	杀戮战场地图("多选框31",11)
	杀戮战场地图("多选框32",12)
	杀戮战场地图("多选框33",13)
	杀戮战场地图("多选框34",14)
	杀戮战场地图("多选框35",15)
	杀戮战场地图("多选框36",16)
	杀戮战场地图("多选框37",17)
	杀戮战场地图("多选框38",18)
	杀戮战场地图("多选框39",19)
	杀戮战场地图("多选框40",20)
end


function 挂星击杀够换图()
	if arr["page1"]["无怪换图"] == "true" and 多点找色(战斗场, 0, 0) == false then
		if 多点找色(击杀满二) or 多点找色(击杀满三) or 多点找色(击杀满, 0, 0) then
			
			显示("已击杀满")
			
			return true
		end
	end
end


function 显示提醒(信息, 次数)
	for i = 次数, 1, -1 do
		sleep(1000)
		显示(信息)
	end
end




function 恶意杀戮提醒()
	if arr["page1"]["地图被恶意刷杀戮boss勾选"] == "true" then
		显示提醒("如果杀戮地图没有被恶意刷杀戮boss,就不要勾选被恶意刷杀戮boss选项,会稍微降低杀戮地图重叠怪的识别效率", 8)
	end
end

function 挂星观看挑战(dt)
	if 多点找色(观看,0,0) or 多点找色(已经有人挑战,0,0)then
		tap(635,341)
		sleep(100)
		
		
		多点找色(挂星左上角图标,1,500)
		
		挂星随机索引 = math.random(1, 2)--"一星"
		-- 获取随机坐标并点击
		local 坐标表 = 挂星小地图坐标表[dt] or 挂星小地图坐标表["十八星"]
		挂星随机坐标 = 坐标表[挂星随机索引]
		
		tap(挂星随机坐标[1], 挂星随机坐标[2])
		sleep(200)
		多点找色(星地图叉,1,2000)
		
		return true
		
	end
end



function 战斗失败不减()
	if 多点找色(失败,0,0)then
		
		失败随机索引 = math.random(1, 2)
		失败随机坐标 = 失败坐标表[失败随机索引]
		tap(失败随机坐标[1], 失败随机坐标[2])
		sleep(200)
	end
end

-- 处理杀戮挑战观看逻辑

function 杀戮挑战观看()
	if 多点找色(观看,0,0) or 多点找色(杀戮下一层,0,0)then
		tap(635,341)
		sleep(200)
		
		
		多点找色(点开杀戮地图,1,300)
		if 多点找色(小地图放大镜,0,0)then
			杀戮随机索引 = math.random(1, #杀戮小地图坐标表)
			-- 获取随机坐标并点击
			杀戮随机坐标 = 杀戮小地图坐标表[杀戮随机索引]
			
			
			
			tap(杀戮随机坐标[1], 杀戮随机坐标[2])
			sleep(200)
			多点找色(杀戮小地图叉,1,1000)
			--break
			return true
		end
		
	end
end





-- 处理神秘挑战
function 神秘挑战观看()
	if 多点找色(观看,0,0) or 多点找色(已经有人挑战,0,0)then
		tap(635,341)
		sleep(300)
		
		
		多点找色(神秘地图,1,300)
		if 多点找色(小地图放大镜,0,0)then
			
			神秘随机索引 = math.random(1, #杀戮小地图坐标表)
			-- 获取随机坐标并点击
			神秘随机坐标 = 杀戮小地图坐标表[神秘随机索引]
			
			
			
			tap(神秘随机坐标[1], 神秘随机坐标[2])
			sleep(200)
			多点找色(杀戮小地图叉,1,500)
			
		end
		return true
		
	end
end

function 争霸挑战观看()
	if 多点找色(观看,0,0) or 多点找色(已经有人挑战,0,0)then
		tap(635,341)
		sleep(200)
		
		
		多点找色(争霸地图,1,800)
		if 多点找色(争霸小地图放大镜,0,0)then
			争霸随机索引 = math.random(1, 5)
			-- 获取随机坐标并点击
			争霸随机坐标 = 争霸坐标表[争霸随机索引]
			
			
			tap(争霸随机坐标[1], 争霸随机坐标[2])
			sleep(500)
			多点找色(争霸小地图叉,1,1000)
			return true
		end
		
	end
end




function 争霸传送(x,y)
	关闭清爽界面()
	if 多点找色(最强王者, 1, 1000) then
		--显示("打开最强王者")
	else
		多点找色(最强王者叉右下角,1,1000)
		
		多点找色(最强王者, 1, 1000)
	end
	if 多点找色(面板传送, 0, 0) then
		
		if  多点找色(传送面板五百争霸,0,0) then
			tap(x, y)
			for i=1,5 do
				sleep(1000)
				if 多点找色(争霸地图内,0,0)then
					显示("到达争霸")
					打开清爽界面()
					return true
				end
			end
		else
			for i=1,6 do
				sleep(1000)
				--多点找色(世界之王确定,1,300)
				显示("没找到关闭再找")
				多点找色 (传送面板叉,1,1000)
				多点找色(最强王者, 1, 1000)
				
				if  多点找色(传送面板五百争霸,0,0) then
					tap(x, y)
					for i=1,5 do
						sleep(1000)
						if 多点找色(争霸地图内,0,0)then
							显示("到达争霸")
							打开清爽界面()
							return true
						end
					end
				end
				
			end
			
		end
		
	end
	return false  -- 传送失败
end



function 四十五挂星传送(x,y)
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	local second = os.date("*t", time).sec
	
	if hour < 18 or (hour == 18 and minute < 55) then
		关闭清爽界面()
		多点找色(只点拒绝,1,500)
		if 多点找色(王者之路, 1, 1000) then
			--显示("打开最强王者")
		else
			
			多点找色(最强王者叉右下角,1,1000)
			
			多点找色(王者之路, 1, 1000)
		end
		--多点找色(世界之王确定,1,300)
		if 多点找色(面板传送, 0, 0) then
			
			swipe(966,635, 962,578, 500)
			sleep(1000)
			if  多点找色(四十六星,0,0) then
				
				tap(x, y)
				sleep(3000)
				打开清爽界面()
				return true
			else
				for i=1,8 do
					sleep(1000)
					显示("没找到关闭再找")
					多点找色 (传送面板叉,1,1000)
					多点找色(王者之路, 1, 1000)
					--多点找色(世界之王确定,1,300)
					swipe(966,635, 962,578, 500)
					sleep(1000)
					if  多点找色(四十六星,0,0) then
						
						tap(x, y)
						sleep(3000)
						打开清爽界面()
						return true
					end
					
					
				end
				
				
			end
		end
	end
end

function 五十六挂星传送(x,y)
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	local second = os.date("*t", time).sec
	
	if hour < 18 or (hour == 18 and minute < 55) then
		关闭清爽界面()
		多点找色(只点拒绝,1,500)
		if 多点找色(王者之路, 1, 1000) then
			--显示("打开最强王者")
		else
			
			多点找色(最强王者叉右下角,1,1000)
			
			多点找色(王者之路, 1, 1000)
		end
		--多点找色(世界之王确定,1,300)
		if 多点找色(面板传送, 0, 0) then
			
			swipe(966,635, 962,94, 500)
			sleep(1000)
			if  多点找色(五十六星,0,0) then
				tap(x, y)
				sleep(3000)
				打开清爽界面()
				return true
			else
				for i=1,8 do
					sleep(1000)
					显示("没找到关闭再找")
					多点找色 (传送面板叉,1,1000)
					多点找色(王者之路, 1, 1000)
					--多点找色(世界之王确定,1,300)
					swipe(966,635, 962,94, 500)
					sleep(1000)
					if  多点找色(五十六星,0,0) then
						
						tap(x, y)
						sleep(3000)
						打开清爽界面()
						return true
					end
					
				end
				
			end
		end
	end
	
end


function 判断分辨率()
	local w,h
	w,h = getDisplaySize()
	--print(w,h)
	if w==720 and h==1280 then
		
		--[===[显示("当前分辨率为"..w.."X"..h.."分辨率正确,记得把同屏人数调成一般否则影响效率")
		sleep(2000)]===]
	else
		
		for i=1,7 do
			显示("当前分辨率为"..w.."X"..h..",分辨率错误,7秒后停止运行,请更换成720X1280分辨率")
			sleep(1000)
			
		end
		exitScript()
	end
end


function 进神秘(小时,smx,smy,hour,minute)
	
	
	if hour == 小时  and minute == 29 and 多点找色(战斗场,0,0)==false then
		for i=1,7 do
			sleep(1000)
			多点找色(只点拒绝,1,500)
			关闭清爽界面()
			if 多点找色(最强王者, 1, 1000) then
				--显示("打开最强王者")
			else
				多点找色(最强王者叉右下角,1,1000)
				
				多点找色(最强王者, 1, 1000)
			end
			if 多点找色(面板传送, 0, 0) then
				
				--print("关掉神秘弹窗")
				多点找色(只点拒绝,1,500)
				
				swipe(524,593, 526,220, 500)
				sleep(1000)
				if  多点找色(神秘活动,0,0) then
					tap(smx, smy)--神秘一191,347
					for i=1,7 do
						sleep(1000)
						if 多点找色(神秘地图,0,0)then
							显示("到达神秘")
							打开清爽界面()
							return true
						end
						
					end
					
					--显示(z)
				else
					显示("没找到关闭再找")
					多点找色 (传送面板叉,1,1000)
					多点找色(最强王者, 1, 1000)
					--多点找色(世界之王确定,1,300)
					swipe(524,593, 526,220, 500)
					sleep(1000)
					if  多点找色(神秘活动,0,0) then
						tap(smx, smy)
						for i=1,7 do
							sleep(1000)
							--多点找色(世界之王确定,1,300)
							if 多点找色(神秘地图,0,0)then
								显示("到达神秘")
								打开清爽界面()
								return true
							end
							
						end
						
						
					end
					
				end
			end
			
		end
		return false  -- 传送失败
	end
end
-- 神秘地图配置表：每个小时对应的坐标和描述
local 神秘地图配置 = {
[0] = {x = 191, y = 347, desc = "神秘 0"},
[1] = {x = 413, y = 342, desc = "神秘 1"},
[2] = {x = 633, y = 344, desc = "神秘 2"},
[3] = {x = 863, y = 342, desc = "神秘 3"},
[4] = {x = 1073, y = 342, desc = "神秘 4"},
[5] = {x = 195, y = 406, desc = "神秘 5"},
[6] = {x = 414, y = 401, desc = "神秘 6"},
[7] = {x = 635, y = 404, desc = "神秘 7"},
[8] = {x = 860, y = 401, desc = "神秘 8"},
[9] = {x = 1066, y = 405, desc = "神秘 9"},
[10] = {x = 193, y = 464, desc = "神秘 10"},
[11] = {x = 405, y = 464, desc = "神秘 11"},
[12] = {x = 630, y = 463, desc = "神秘 12"},
[13] = {x = 859, y = 462, desc = "神秘 13"},
[14] = {x = 1077, y = 464, desc = "神秘 14"},
[15] = {x = 197, y = 527, desc = "神秘 15"},
[16] = {x = 421, y = 524, desc = "神秘 16"},
[17] = {x = 642, y = 524, desc = "神秘 17"},
[18] = {x = 861, y = 524, desc = "神秘 18"}
}

function 参加神秘(hour, minute)
	-- 检查当前小时是否在有效范围内（0-18点，18点30分还有最后一波）
	if hour < 0 or hour > 18 then
		return false
	end
	
	-- 获取当前小时对应的神秘地图配置
	local 配置 = 神秘地图配置[hour]
	if not 配置 then
		return false
	end
	
	-- 尝试进入对应的神秘地图
	if 进神秘(hour, 配置.x, 配置.y, hour, minute) then
		print("进入" .. 配置.desc)
		
		-- 成功进入神秘地图后，执行神秘活动并返回结果
		local 神秘结果 = 神秘()
		if 神秘结果 then
			显示("神秘活动完成，返回挂星")
			
			return true
		else
			显示("神秘活动未执行或失败")
			return false
		end
	end
	
	return false
end

function 同屏人数()
	if 多点找色(综合功能, 1, 500) then
		多点找色(简化设置, 1, 500)
		if 多点找色(基础设置叉,0,0) then
			for i=1,10 do
				tap(389,447)
				sleep(500)
				tap(512,442)
				sleep(500)
				if 多点找色(一般,0,0)then
					break
				end
			end
		end
		if 多点找色(基础设置叉,1,500) then
			tap(635,341)
			sleep(500)
		end
		
	else
		if 多点找色(设置, 1, 500) then
			--显示("打开最强王者")
		else
			多点找色(最强王者叉右下角,1,500)
			
			多点找色(设置, 1, 500)
		end
		if 多点找色(基础设置叉,0,0) then
			for i=1,10 do
				tap(389,447)
				sleep(500)
				tap(512,442)
				sleep(500)
				if 多点找色(一般,0,0)then
					break
				end
			end
		end
		多点找色(基础设置叉,1,500)
	end
	
end


function 打开清爽界面()
	if not 多点找色(综合功能, 0, 0) then
		if 多点找色(设置, 1, 500) then
			--显示("打开最强王者")
		else
			多点找色(最强王者叉右下角,1,500)
			多点找色(设置, 1, 500)
		end
		if 多点找色(清爽,1,500) then
			多点找色(基础设置叉,1,500)
			return true
		end
	end
end
function 关闭清爽界面()
	if not 多点找色(设置, 0, 0) then
		多点找色(综合功能, 1, 500)
		多点找色(简化设置, 1, 500)
		if 多点找色(关闭清爽,1,500) then
			多点找色(基础设置叉,1,500)
			return true
		end
	end
end
当前加速值 = 0
上次加速更新时间 = os.time()
加速更新时间间隔 = 2
function 加速(c)
	if (os.time() - 上次加速更新时间) < 加速更新时间间隔 then
		return
	end
	local 模块 =app.GetModuleHandle(pkgname, "libgame.so")
	local bss地址 = 模块 + 0xD37684
	local 读bss地址 = app.MemoryRead(pkgname, bss地址, "u32")
	local 基地址 = 读bss地址 ~= 0 and 读bss地址 or bss地址
	local 偏移1 = app.MemoryRead(pkgname, 基地址+ 0x78, "u32")
	local 偏移2 = app.MemoryRead(pkgname, 偏移1 + 0x11C, "u32")
	if 当前内存加速值 ~= c then
		结果=app.MemoryWrite(pkgname, 偏移2 + 0x20, c, "f32")
		当前加速值 = c
		上次加速更新时间 = os.time()
	end
end

function 瞬移(x,y)
	
	local 模块 = app.GetModuleHandle(pkgname, "libgame.so")
	local bss地址 = 模块 + 0xD37684
	local 读bss地址 = app.MemoryRead(pkgname, bss地址, "u32")
	local 基地址 = 读bss地址 ~= 0 and 读bss地址 or bss地址
	local 偏移1 = app.MemoryRead(pkgname, 基地址+ 0x78, "u32")
	local 偏移2 = app.MemoryRead(pkgname, 偏移1 + 0x11C, "u32")
	app.MemoryWrite(pkgname, 偏移2 + 0x20-20, x*24, "f32")
	app.MemoryWrite(pkgname, 偏移2 + 0x20-16, y * 16, "f32")
	sleep(1500)
	tap(648,362)
	sleep(500)
end



function 卡地图挂星()
	if 多点找色(神秘地图,0,0) or 多点找色(卡杀戮战场,0,0) or 多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0) then
		
		显示("卡地图")
		return true
	end
end
function 卡地图世界()
	if 多点找色(神秘地图,0,0) or 多点找色(卡杀戮战场,0,0) then
		
		显示("卡地图")
		return true
	end
end
function 卡地图争霸()
	if 多点找色(神秘地图,0,0) or 多点找色(卡杀戮战场,0,0)or 多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0) then
		
		显示("卡地图")
		return true
	end
end
function 卡地图杀戮()
	if 多点找色(神秘地图,0,0) or  多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0) then
		
		显示("卡地图")
		return true
	end
end
function 通用卡怪处理(卡怪状态, 坐标表, 地图叉图标, 随机数量, 提示信息)
	if 卡怪状态==false and not 多点找色(战斗场,0,0) then
		toast("卡怪", 35, 645, 12)
		多点找色(挂星左上角图标,1,500)
		
		-- 如果没有指定随机数量，默认使用坐标表的长度
		local 最大随机数 = 随机数量 or #坐标表
		local 随机索引 = math.random(1, 最大随机数)
		-- 获取随机坐标并点击
		local 随机坐标 = 坐标表[随机索引]
		
		tap(随机坐标[1], 随机坐标[2])
		sleep(200)
		多点找色(地图叉图标,1,1000)
		
		return true -- 返回true表示已处理卡怪
	end
	return false -- 返回false表示未处理
end
function 挂星卡怪处理(dt, 卡怪状态)
	-- 获取坐标表：19星以后都使用18星的坐标
	local 坐标表 = 挂星小地图坐标表[dt]
	if not 坐标表 then
		-- 如果找不到对应的坐标表，使用18星的坐标（19星以后都一样）
		坐标表 = 挂星小地图坐标表["十八星"]
	end
	
	return 通用卡怪处理(卡怪状态, 坐标表, 星地图叉, 2, "怪物卡屏走开")
end

function 争霸卡怪处理(dt, 卡怪状态)
	-- 争霸使用争霸坐标表，参考争霸怪物卡屏函数
	if 卡怪状态==false and not 多点找色(战斗场,0,0) then
		显示("怪物卡屏走开")
		多点找色(挂星左上角图标,1,500)
		
		争霸随机索引 = math.random(1, 5)
		-- 获取随机坐标并点击
		争霸随机坐标 = 争霸坐标表[争霸随机索引]
		
		tap(争霸随机坐标[1], 争霸随机坐标[2])
		sleep(500)
		多点找色(争霸小地图叉,1,1000)
		return true
	end
	return false
end
-- 改进的瞬移函数，解决交互问题
function 瞬移(x, y, 等待交互)
	-- 等待交互参数：true表示瞬移后等待并尝试交互，false或nil表示仅瞬移
	等待交互 = 等待交互 or false

	local 模块 = app.GetModuleHandle(pkgname, "libgame.so")
	local bss地址 = 模块 + 0xD37684
	local 读bss地址 = app.MemoryRead(pkgname, bss地址, "u32")
	local 基地址 = 读bss地址 ~= 0 and 读bss地址 or bss地址
	local 偏移1 = app.MemoryRead(pkgname, 基地址+ 0x78, "u32")
	local 偏移2 = app.MemoryRead(pkgname, 偏移1 + 0x11C, "u32")

	-- 执行瞬移
	app.MemoryWrite(pkgname, 偏移2 + 0x20-20, x*24, "f32")
	app.MemoryWrite(pkgname, 偏移2 + 0x20-16, y * 16, "f32")

	if 等待交互 then
		-- 等待游戏同步和视野刷新
		sleep(1500)

		-- 点击确认位置，让角色"走"到目标位置
		tap(648, 362)
		sleep(800)

		-- 额外等待，确保怪物/NPC加载完成
		sleep(1000)

		-- 可选：小幅移动来触发交互检测
		local 微调坐标 = {{646, 360}, {650, 364}, {644, 358}}
		local 随机索引 = math.random(1, #微调坐标)
		tap(微调坐标[随机索引][1], 微调坐标[随机索引][2])
		sleep(500)

		显示("瞬移完成，等待交互")
		return true
	else
		-- 仅瞬移，不等待交互
		sleep(500)
		return true
	end
end

-- 专门用于怪物/NPC交互的瞬移函数
function 瞬移到目标(x, y)
	return 瞬移(x, y, true)
end

-- 瞬移并尝试与怪物交互的完整流程
function 瞬移攻击怪物(x, y, 怪物类型)
	-- 怪物类型: "世界boss怪物", "星怪", "杀戮战场阶" 等
	怪物类型 = 怪物类型 or 怪物  -- 默认使用通用怪物

	显示("开始瞬移到怪物位置")

	-- 瞬移到目标位置
	if 瞬移到目标(x, y) then
		-- 尝试多次寻找和攻击怪物
		for i = 1, 8 do
			sleep(300)

			-- 根据怪物类型使用不同的查找函数
			local 找到怪物 = false
			if 怪物类型 == "世界boss怪物" then
				找到怪物 = 多点找色世界(世界boss怪物, 1, 200)
			elseif 怪物类型 == "星怪" then
				找到怪物 = 多点找色星(星怪, 1, 200)
			elseif 怪物类型 == "杀戮战场阶" then
				找到怪物 = 多点找色阶(杀戮战场阶, 1, 200)
			else
				-- 默认使用争霸怪物查找
				找到怪物 = 多点找色争霸(怪物, 1, 200)
			end

			if 找到怪物 then
				显示("找到怪物，尝试进入战斗")
				-- 检查是否成功进入战斗
				if 通用进入战斗() then
					显示("瞬移攻击成功")
					return true
				end
			else
				-- 如果没找到怪物，尝试小范围移动
				local 搜索坐标 = {{x-10, y}, {x+10, y}, {x, y-10}, {x, y+10}}
				local 随机坐标 = 搜索坐标[math.random(1, #搜索坐标)]
				tap(随机坐标[1], 随机坐标[2])
				sleep(400)
			end
		end

		显示("瞬移后未找到可攻击的怪物")
		return false
	else
		显示("瞬移失败")
		return false
	end
end
