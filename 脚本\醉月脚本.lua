
require("醉月脚本卡")
require("醉月脚本变")
require("醉月脚本函")
require("Memory")
app=require("Memory")
math.randomseed(os.time())

for i=41,60 do
	if arr["page2"]["多选框"..i] == "true" then
		
		世界勾选层数=世界勾选层数+1
		
	end

end
setControlBarPosNew(0, 0.7)
判断分辨率()
恶意杀戮提醒()
同屏人数()
local 初始颜色1 = nil
local 初始颜色2 = nil
local 初始颜色3 = nil
local 结束颜色1= nil
local 结束颜色2= nil
local 结束颜色3= nil
local 卡屏次数 = 0
local 卡屏检测时间 = tickCount()

-- 单线卡屏检测函数
function 卡屏检测()
	local 当前时间 = tickCount()
	
	if 初始颜色1 == nil or 初始颜色2 == nil or 初始颜色3 == nil then
		-- 如果还未设置初始颜色，等待时间到后设置
		if 当前时间 - 卡屏检测时间 >= 10000 then
			-- 获取两个点的初始颜色
			if 初始颜色1 == nil then
				初始颜色1 = getPixelColor(137,70)
				print("初始颜色1: " .. 初始颜色1)
			end
			if 初始颜色2 == nil then
				初始颜色2 = getPixelColor(148,83)  -- 另一个点的坐标
				print("初始颜色2: " .. 初始颜色2)
			end
			if 初始颜色3 == nil then
				初始颜色3 = getPixelColor(175,76)  -- 另一个点的坐标
				print("初始颜色3: " .. 初始颜色3)
			end
			卡屏检测时间 = 当前时间
		end
	else
		-- 检测两个点的颜色变化
		if 当前时间 - 卡屏检测时间 >= 10000 then
			结束颜色1 = getPixelColor(137,70)
			结束颜色2 = getPixelColor(148,83)
			结束颜色3 = getPixelColor(175,76)  -- 另一个点的坐标
			print("对比颜色1: " .. 结束颜色1)
			print("对比颜色2: " .. 结束颜色2)
			print("对比颜色3: " .. 结束颜色3)
			if 初始颜色1 == 结束颜色1 and 初始颜色2 == 结束颜色2 and 初始颜色3 == 结束颜色3 then
				卡屏次数 = 卡屏次数 + 1
				print("卡屏次数: " .. 卡屏次数)
			else
				print("没有卡屏")
				卡屏次数 = 0
				初始颜色1 = getPixelColor(137,70)  -- 更新初始颜色
				初始颜色2 = getPixelColor(148,83)
				初始颜色3 = getPixelColor(175,76) -- 更新初始颜色
			end
			
			卡屏检测时间 = 当前时间
		end
	end
	
	if 卡屏次数 >= 2 then
		print("卡屏检测警告: 卡屏次数达到 " .. 卡屏次数)
		卡屏次数 = 0
		
		
		stopApp("ccz.locojoy.mini.mt34.joysdk")
		sleep(2000)
		
		local ret = appIsFront("ccz.locojoy.mini.mt34.joysdk")--appIsRunning
		if ret == false then
			for i=1,50 do
				sleep(1000)
				
				多点找色(登录,1,1000)
				多点找色(立即登录,1,1000)
				多点找色(立即登录二,1,1000)
				多点找色(关闭,1,1000)
				多点找色(开始游戏,1,1000)
				多点找色(开始游戏合区,1,1000)
				多点找色(只点拒绝,1,500)
				多点找色(奇缘拒绝,1,500)
				多点找色(奖励叉,1,1000)
				-- 移除递归调用各类弹窗()，避免无限递归
				if 多点找色(活动图标,0,0) or 多点找色(战斗场,0,0)or 多点找色(背包, 0, 0)then
					print("游戏重启成功")
					--坐骑()
					return true
				end
				
				
			end
		end
	end
end

function 争霸(a, x, y, dt)
	if arr["page2"][a] == "true" then
		local 无怪=0
		local 卡怪=true
		if 争霸传送(x,y) then
			while 1 do
				sleep(500)
				
				local time = os.time()
				local hour = os.date("*t", time).hour
				local minute = os.date("*t", time).min
				
				if hour < 18 or (hour == 18 and minute < 55) then
					
					
					显示("在争霸")
					加速(20)
					各类弹窗()
					--[===[if 卡屏检测()then
					return
					end]===]
					多点找色(只点拒绝,1,300)
					多点找色(物品弹窗,1,300)
					if 多点找色争霸(怪物, 1, 100) then
						无怪=0
						卡怪=false
						for i=1,10 do
							sleep(200)
							if  多点找色(只点拒绝,1,400) then
								多点找色争霸(怪物, 1, 200)
							end
							if 多点找色重叠(怪物重叠阶,0,0)then
								tap(646,340)
								sleep(200)
								
							elseif 通用进入战斗() then
								多点找色(只点拒绝,1,300)
								卡怪=true
								break
								
							elseif 争霸挑战观看() then
								break
							end
							sleep(200)
						end
						争霸卡怪处理(dt, 卡怪)
					else
						无怪=无怪+1
					end
					多点找色(只点拒绝,1,300)
					if 卡地图争霸() then
						
						return
					end
					
					if arr["page1"]["神秘分次进"]=="true" then
						多点找色(只点拒绝,1,100)
						if 参加神秘(hour,minute) and 多点找色(战斗场,0,0)==false then
							显示("神秘活动完成，返回争霸")
							争霸传送(x,y)  -- 重新传送回争霸地图
						end
					end
					
					if 无怪 >= 5 and not 多点找色(战斗场, 0, 0) then
						无怪=0
						多点找色(只点拒绝,1,100)
						local 找到怪 = 0  -- 用于判断是否找到怪物
						-- 遍历所有坐标
						for i, 坐标 in ipairs (争霸坐标表) do
							加速(20)
							--多点找色(世界之王确定,1,300)
							
							if 多点找色(挂星左上角图标, 1, 400) then
								tap(坐标[1], 坐标[2])
								sleep(500)
							end
							多点找色(争霸小地图叉,1,1000)
							for j = 1, 10 do
								sleep(200)  -- 等待300ms
								
								if   多点找色世界(怪物, 0, 0) then
									找到怪=1
									break
								end
							end
							
							--print(找到怪)
							if 找到怪 == 1 then
								
								break  -- 跳出遍历所有坐标的循环
							end
						end
						if 找到怪 == 0 then
							显示("争霸没怪")
							
							return
						end
						
					end
				else
					return
				end
				
			end
		end
	end
end
function 世界boss滑动传送(x, y)
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	if hour < 18 or (hour == 18 and minute < 55) then
		关闭清爽界面()
		多点找色(只点拒绝,1,500)
		if 多点找色(最强王者, 1, 1000) then
		else
			多点找色(最强王者叉右下角,1,1000)
			多点找色(最强王者, 1, 1000)
		end
		if 多点找色(面板传送, 0, 0) then
			swipe(839, 457, 853, 391, 500)  -- 滑动操作
			sleep(1000)
			if 多点找色(世界boss滑动参照,0,0) then
				显示("到达世界boss")
				tap(x, y)
				sleep(3000)
				if not 多点找色(神秘地图,0,0) and (多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0)) then
					toast("到达世界boss", 35, 645, 12)
					if 打开清爽界面() then
						return true
					end
				end
			else
				for i=1,8 do
					sleep(1000)
					显示("没找到，重试")
					多点找色(传送面板叉,1,1000)
					多点找色(最强王者, 1, 1000)
					if 多点找色(面板传送,0,0) then
						swipe(839, 457, 853, 391, 500)
						sleep(1000)
						if 多点找色(世界boss滑动参照,0,0) then
							显示("到达世界boss")
							tap(x, y)
							sleep(3000)
							if not 多点找色(神秘地图,0,0) and (多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0)) then
								toast("到达世界boss", 35, 645, 12)
								if 打开清爽界面() then
									return true
								end
							end
						end
					end
				end
			end
		end
	end
end

function 世界传送(x, y)
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	local second = os.date("*t", time).sec
	if hour < 18 or (hour == 18 and minute < 55) then
		关闭清爽界面()
		多点找色(只点拒绝,1,500)
		if 多点找色(最强王者, 1, 1000) then
			--print("打开最强王者")
		else
			多点找色(最强王者叉右下角,1,1000)
			多点找色(最强王者, 1, 1000)
		end
		if 多点找色(面板传送, 0, 0) then
			if 多点找色(世界面板参照,0,0) then
				多点找色(只点拒绝,1,500)
				tap(x, y)
				sleep(3000)
				if not 多点找色(神秘地图,0,0) and (多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0)) then
					toast("到达世界boss", 35, 645, 12)
					if 打开清爽界面() then
						return true
					end
				end
			else
				for i=1,8 do
					sleep(1000)
					toast("没找到关闭再找", 35, 645, 12)
					多点找色(传送面板叉,1,1000)
					多点找色(最强王者, 1, 1000)
					if 多点找色(面板传送,0,0) then
						if 多点找色(世界面板参照,0,0) then
							tap(x, y)
							sleep(3000)
							if not 多点找色(神秘地图,0,0) and (多点找色(世界boss地图识别,0,0) or 多点找色(世界boss地图识别一,0,0)) then
								toast("到达世界boss", 35, 645, 12)
								if 打开清爽界面() then
									return true
								end
							end
						end
					end
				end
			end
		end
	end
end
世界boss层数=0
世界可打=0
function 世界boss地图(s, x, y, 需要滑动)
	if 多点找色(奖励, 0, 0) and 多点找色(奖励叉, 1, 500) then
		-- 处理奖励逻辑
	end
	if arr["page2"][s] == "true" and 世界可打==0 then
		-- 根据是否需要滑动选择不同的传送函数
		local 传送成功 = false
		if 需要滑动 then
			传送成功 = 世界boss滑动传送(x, y)
		else
			传送成功 = 世界传送(x, y)
		end
		
		if 传送成功 then
			local 无怪=0
			while true do
				sleep(200)
				local time = os.time()
				local hour = os.date("*t", time).hour
				local minute = os.date("*t", time).min
				local second = os.date("*t", time).sec
				if hour < 18 or (hour == 18 and minute < 55) then
					显示("在世界boss")
					加速(20)
					各类弹窗()
					--[===[if 卡屏检测()then
					return
					end]===]
					if arr["page1"]["丢丹"] == "true" then
						if (minute == 28 or minute == 29) then
							-- 在28分或29分钟时不执行丢丹，跳过这一步
						else
							if math.floor(tickCount() - 开始丢丹) >= (tonumber(arr["page1"]["丢丹分"]) * 60000) and not 多点找色(战斗场, 0, 0) then
								丢丹()
								开始丢丹 = tickCount()
							end
						end
					end
					-- 神秘活动处理 - 优化后的逻辑
					if arr["page1"]["神秘分次进"] == "true" then
						多点找色(只点拒绝,1,100)
						if 参加神秘(hour, minute) and 多点找色(战斗场,0,0)==false then
							-- 神秘活动完成后，重新传送到当前世界boss地图
							显示("神秘活动完成，返回当前世界boss地图")
							if 需要滑动 then
								世界boss滑动传送(x, y)
							else
								世界传送(x, y)
							end
						end
					end
					多点找色(只点拒绝,1,300)
					多点找色(物品弹窗,1,300)
					if 多点找色世界(世界boss怪物, 1, 100) then
						无怪=0
						for i=1,10 do
							sleep(200)
							if  多点找色(只点拒绝,1,400) then
								多点找色世界(世界boss怪物, 1, 200)
							end
							if 多点找色重叠(怪物重叠阶,0,0)then
								tap(646,340)
								sleep(200)
							end
							if 通用进入战斗() then
								多点找色(只点拒绝,1,300)
								break
							elseif 杀戮挑战观看() then
								break
							end
							
						end
					else
						if not 多点找色(战斗场, 0, 0) then
							无怪=无怪+1
						end
					end
					多点找色(只点拒绝,1,300)
					if 无怪 >= 10 then
						多点找色(点开杀戮地图, 1, 1000)
						local 杀戮找怪坐标 = 获取顺序坐标()
						tap(杀戮找怪坐标[1], 杀戮找怪坐标[2])
						sleep(1000)
						多点找色(杀戮小地图叉, 1, 1000)
						sleep(1000)
						无怪 = 0
					end
					if 卡地图世界()  then
						return
					end
					if 世界击杀满等() then
						-- 当前层已打满，层数已在函数内增加，返回到主循环进行下一层
						return
					end
				else
					return
				end
			end
		end
	end
end
function 世界boss封装()
	-- 按顺序尝试每一层世界boss地图，直到打够层数或所有层都打满
	local 世界boss地图列表 = {
	{"多选框41", 182, 512, false},      -- 第1层
	{"多选框42", 393, 513, false},      -- 第2层
	{"多选框43", 609, 516, false},      -- 第3层
	{"多选框44", 836, 515, false},      -- 第4层
	{"多选框45", 1050, 511, false},     -- 第5层
	{"多选框46", 178, 571, false},      -- 第6层
	{"多选框47", 404, 572, false},      -- 第7层
	{"多选框48", 830, 574, false},      -- 第8层
	{"多选框49", 856, 477, false},      -- 第9层
	{"多选框50", 1058, 571, false},     -- 第10层
	{"多选框51", 184, 632, false},      -- 第11层
	{"多选框52", 404, 631, false},      -- 第12层
	{"多选框53", 615, 631, false},      -- 第13层
	{"多选框54", 831, 632, false},      -- 第14层
	{"多选框55", 1040, 630, false},     -- 第15层
	{"多选框56", 163, 594, true},       -- 第16层（需要滑动）
	{"多选框57", 384, 591, true},       -- 第17层（需要滑动）
	{"多选框58", 614, 591, true},       -- 第18层（需要滑动）
	{"多选框59", 836, 589, true},       -- 第19层（需要滑动）
	{"多选框60", 1046, 590, true}       -- 第20层（需要滑动）
	}
	
	-- 遍历所有勾选的世界boss地图
	for i, 地图信息 in ipairs(世界boss地图列表) do
		local 选项名, x, y, 需要滑动 = 地图信息[1], 地图信息[2], 地图信息[3], 地图信息[4]
		
		-- 检查是否勾选了该层且还没打够层数
		if arr["page2"][选项名] == "true" and 世界可打 == 0 then
			--显示("尝试进入第" .. i .. "层世界boss（已完成" .. 世界boss层数 .. "层）")
			
			-- 使用统一的世界boss地图函数，通过参数控制是否滑动
			世界boss地图(选项名, x, y, 需要滑动)
			
			-- 注意：层数增加逻辑已经在世界击杀满等()函数中处理
			-- 如果已经打够层数，退出循环
			if 世界可打 == 1 then
				break
			end
		end
	end
end

function 杀戮爬楼(number)
	
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	if hour > 18  or (hour == 18 and os.date("*t", time).min >= 55) then
		
		多点找色(只点拒绝,1,500)
		关闭清爽界面()
		if 多点找色(最强王者, 1, 1000) then
		else
			多点找色(最强王者叉右下角,1,1000)
			多点找色(最强王者, 1, 1000)
		end
		if 多点找色(面板传送, 0, 0) then
			swipe(625,655, 633,81, 500)
			sleep(1000)
			if 多点找色(杀戮战场面板, 1, 1000) then
				for i=1,30 do
					sleep(600)
					if 多点找色(杀戮一层,0,0)then
						toast("到达杀戮", 35, 645, 12)
						sleep(1000)
						if number==1then
							打开清爽界面()
							return true
						end
						break
					end
				end
			else
				
				for i=1,8 do
					sleep(500)
					多点找色 (传送面板叉,1,1000)
					
					if 多点找色(最强王者, 1, 1000) then
					else
						多点找色(最强王者叉右下角,1,1000)
						多点找色(最强王者, 1, 1000)
					end
					if 多点找色(面板传送, 0, 0) then
						swipe(625,655, 633,81, 500)
						sleep(1000)
						if 多点找色(杀戮战场面板, 1, 1000) then
							for i=1,30 do
								sleep(600)
								if 多点找色(杀戮一层,0,0)then
									toast("到达杀戮", 35, 645, 12)
									sleep(1000)
									if number==1then
										打开清爽界面()
										return true
									end
									break
								end
							end
							
							
						end
					end
					if 多点找色(杀戮一层,0,0)then
						break
					end
				end
				
				
			end
		end
		if 多点找色(杀戮一层, 0, 0) then
			if number==2 then
				if  not 多点找色重叠(怪物重叠阶,0,0)then
					多点找色(物品弹窗,1,1000)
					tap(856,352)--点击领路人
					sleep(1500)
				else
					--print("重叠不点")
				end
				多点找色(杀戮领路人重叠,1,1500)
				if 多点找色(杀戮下一层,1,1000) then
					for i=1,30 do
						sleep(600)
						if  多点找色(杀戮二层,0,0)then
							toast("已爬到二楼", 35, 645, 12)
							if 打开清爽界面() then
								return true
							end
						end
					end
				end
			elseif number>2 then
				if  not 多点找色重叠(怪物重叠阶,0,0)then
					多点找色(物品弹窗,1,1000)
					tap(856,352)--点击领路人
					sleep(1500)
				else
					--print("重叠不点")
				end
				多点找色(杀戮领路人重叠,1,1500)
				if 多点找色(杀戮下一层,1,1000) then
					for i=1,30 do
						sleep(600)
						if  多点找色(杀戮二层,0,0)then
							toast("已爬到二楼", 35, 645, 12)
							break
						end
					end
				end
				if  多点找色(杀戮二层,0,0)then
					for i=3,number do
						for i=1,30 do
							sleep(600)
							if  not 多点找色重叠(怪物重叠阶,0,0)then
								多点找色(物品弹窗,1,1000)
								tap(665,291)--二层往后杀戮领路人
								sleep(1500)
							else
								--print("重叠不点")
							end
							多点找色(杀戮领路人重叠,1,1500)
							if 多点找色(杀戮下一层,1,1000) then
								for i=1,30 do
									sleep(600)
									if 多点找色(杀戮战场,0,0 ) then
										toast("已经到达下一层", 35, 645, 12)
										break
									end
								end
								break
							end
						end
					end
					if 打开清爽界面() then
						toast("已经到达目的地", 35, 645, 12)
						return true
					end
				end
			end
		end
	end
end
function 杀戮战场地图(bb,number)
	if arr["page2"][bb] == "true"  then
		local 无怪=0
		if 杀戮爬楼(number) then
			while 1 do
				sleep(200)
				local time = os.time()
				local hour = os.date("*t", time).hour
				local minute = os.date("*t", time).min
				if hour > 18  or (hour == 18 and os.date("*t", time).min >= 55) then
					
					加速(20)
					
					各类弹窗()
					--[===[if 卡屏检测()then
					return
					end]===]
					if 多点找色(轮回寺,0,0) then
						显示("在轮回寺重新回到杀戮")
						return
						
					end
					分解封装()
					
					if arr["page1"]["丢丹"] == "true" then
						if (minute == 28 or minute == 29) then
							-- 在28分或29分钟时不执行丢丹，跳过这一步
						else
							if math.floor(tickCount() - 开始丢丹) >= (tonumber(arr["page1"]["丢丹分"]) * 60000) and not 多点找色(战斗场, 0, 0) then
								丢丹()
								开始丢丹 = tickCount()
							end
						end
					end
					多点找色(只点拒绝,1,300)
					多点找色(物品弹窗,1,300)
					if 多点找色阶(杀戮战场阶, 1, 100) then
						for i=1,10 do
							sleep(200)
							if  多点找色(只点拒绝,1,400) then
								print("只点拒绝")
								多点找色阶(杀戮战场阶, 1, 200)
							end
							if arr["page1"]["地图被恶意刷杀戮boss勾选"] == "false" then
								if 多点找色重叠(怪物重叠阶,0,0)then
									tap(646,340)
									sleep(200)
								end
							else
								多点找色重叠(重叠阶,1,200)
							end
							
							if 通用进入战斗() then
								
								多点找色(只点拒绝,1,300)
								
								break
							elseif 杀戮挑战观看() then
								
								break
							end
							
						end
						
					else
						
						if hour ~= 21 and not 多点找色(战斗场, 0, 0) then
							无怪 = 无怪 + 1
							sleep(1000)
							--print(杀戮无怪)
							
							if 无怪 >= 5 then
								多点找色(点开杀戮地图, 1, 1000)
								
								local 杀戮找怪坐标 = 获取顺序坐标()
								tap(杀戮找怪坐标[1], 杀戮找怪坐标[2])
								sleep(1000)
								多点找色(杀戮小地图叉, 1, 1000)
								sleep(1000)
								
								无怪 = 0
							end
						end
					end
					多点找色(只点拒绝,1,300)
					
					if arr["page1"]["华山"]=="true"   then
						if hour==21 and minute==5 then
							
							if 华山() then
								return true
							end
						end
					end
				else
					
					return
				end
			end
		end
	end
end

function 通用进入战斗()
	-- 尝试找到进入战斗的界面
	if 多点找色(进入战斗, 1, 500) then
		print("2")
		for i = 1, 10 do
			sleep(200)
			if 多点找色(战斗场, 0, 0) then
				while 1 do
					sleep(200)
					多点找色(自动, 1, 200)
					多点找色(取消, 1, 200)
					多点找色(只点拒绝, 1, 400)
					if not 多点找色(战斗场, 0, 0) then
						return true
					end
				end
				return true
			end
		end
	end
end
function 星传送(x, y)
	local time = os.time()
	local hour = os.date("*t", time).hour
	local minute = os.date("*t", time).min
	local second = os.date("*t", time).sec
	
	if hour < 18 or (hour == 18 and minute < 55) then
		关闭清爽界面()
		if 多点找色(王者之路, 1, 1000) then
			--显示("打开最强王者")
		else
			多点找色(最强王者叉右下角,1,1000)
			多点找色(王者之路, 1, 1000)
		end
		if 多点找色(面板传送, 0, 0) then
			if 多点找色(传送面板一星,0,0)then
				print("面板")
				tap(x, y)
				sleep(3000)
				if 打开清爽界面() then
					return true
				end
			else
				for i=1,8 do
					sleep(1000)
					toast("没找到关闭再找", 35, 645, 12)
					多点找色 (传送面板叉,1,1000)
					多点找色(王者之路, 1, 1000)
					if  多点找色(面板传送,0,0) then
						if 多点找色(传送面板一星,0,0)then
							tap(x, y)
							sleep(3000)
							if 打开清爽界面() then
								return true
							end
						end
					end
				end
			end
		end
	end
	return false
end
function 挂星地图通用(x, y, dt, 传送函数, 找怪函数)
	-- 如果没有指定传送函数，默认使用星传送
	传送函数 = 传送函数 or 星传送
	-- 如果没有指定找怪函数，默认使用普通找怪
	找怪函数 = 找怪函数 or function() return 多点找色星(星怪, 1, 200) end
	
	if 传送函数(x, y) then
		
		local 无怪=0
		local 卡怪=false
		local 卡怪次数=0
		while true do
			sleep(200)
			local time = os.time()
			local hour = os.date("*t", time).hour
			local minute = os.date("*t", time).min
			local second = os.date("*t", time).sec
			
			if hour < 18 or (hour == 18 and minute < 55) then
				
				
				if arr["page1"]["丢丹"] == "true" then
					if (minute == 0 or minute == 29) then
						-- 在28分或29分钟时不执行丢丹，跳过这一步
					else
						if math.floor(tickCount() - 开始丢丹) >= (tonumber(arr["page1"]["丢丹分"]) * 60000) and not 多点找色(战斗场, 0, 0) then
							丢丹()
							开始丢丹 = tickCount()
						end
					end
				end
				if arr["page2"]["争霸刷新才打"] == "true" then
					if minute == 0 and not 多点找色(战斗场,0,0) then
						显示("争霸刷新")
						争霸boss()
						传送函数(x, y)
					end
				end
				加速(20)
				
				if arr["page1"]["神秘分次进"] == "true" and not 多点找色(战斗场,0,0) then
					-- 尝试参加神秘活动，如果成功则直接返回
					if 参加神秘(hour, minute) then
						显示("返回继续挂星")
						传送函数(x, y)
					end
				end
				
				各类弹窗()
				--[===[if 卡屏检测()then
				return
				end]===]
				分解封装()
				if  挂星击杀够换图() then
					
					return
				end
				多点找色(只点拒绝,1,300)
				多点找色(物品弹窗,1,300)
				if   找怪函数()   then
					
					无怪=0
					卡怪=false
					for i = 1, 15 do
						sleep(200)
						if  多点找色(只点拒绝,1,400) then
							找怪函数()
						end
						if 多点找色重叠(怪物重叠阶,0,0)then
							tap(646,340)
							sleep(500)
							print("1")
							
						end
						
						--挂星战斗中规避()
						if 通用进入战斗() then
							卡怪=true
							卡怪次数=0
							多点找色(只点拒绝,1,300)
							break
						elseif 挂星观看挑战(dt) then
							print("退出")
							break
						end
						
					end
					多点找色(只点拒绝,1,300)
					if 卡怪 == false and not 多点找色(战斗场,0,0) then
						print("尝试小范围移动")
						local 小范围坐标表 = {{602,346}, {666,346}}
						local 随机索引 = math.random(1, 2)
						local 随机坐标 = 小范围坐标表[随机索引]
						tap(随机坐标[1], 随机坐标[2])
						sleep(500)
						
						
						-- 移动后再次检查是否有怪
						if 找怪函数() then
							print("小范围移动后找到怪")
							-- 重新尝试战斗
							for j = 1, 10 do
								sleep(200)
								if  多点找色(只点拒绝,1,400) then
									找怪函数()
								end
								if 多点找色重叠(怪物重叠阶,0,0)then
									tap(646,340)
									sleep(500)
									print("1")
									
								end
								
								--挂星战斗中规避()
								if 通用进入战斗() then
									卡怪=true
									卡怪次数=0
									多点找色(只点拒绝,1,300)
									break
								elseif 挂星观看挑战(dt) then
									print("退出")
									break
								end
								
							end
						end
					end
					if 挂星卡怪处理(dt, 卡怪) then
						卡怪次数=卡怪次数+1
					end
					
				elseif not 多点找色(战斗场,0,0) then
					无怪=无怪+1
				end
				
				print("卡怪次数"..卡怪次数)
				if 卡地图挂星() or 卡怪次数>=5 then
					卡怪次数=0
					return
				end
				
				if 无怪 >= 10 and not 多点找色(战斗场, 0, 0) then
					
					无怪=0
					多点找色(只点拒绝,1,100)
					
					local 找到怪 = 0  -- 用于判断是否找到怪物
					-- 遍历所有坐标
					for i, 坐标 in ipairs(挂星小地图坐标表[dt]) do
						加速(20)
						
						--多点找色(世界之王确定,1,300)
						if 多点找色(挂星左上角图标, 1, 400) then
							tap(坐标[1], 坐标[2])
							sleep(200)
						end
						多点找色(星地图叉,1,1000)
						for j = 1, 10 do
							sleep(200)  -- 等待300ms
							
							if   找怪函数() then
								找到怪=1
								break
							end
						end
						
						-- 如果找到怪物，退出循环
						if 找到怪 == 1 then
							--print("退出找怪遍历")
							break  -- 跳出遍历所有坐标的循环
						end
					end
					
					if 找到怪 == 0 then
						if arr["page1"]["无怪换图"] == "true" and 多点找色(战斗场, 0, 0) == false then
							return
						end
					end -- 这里你可以根据需要替换为其他的退出或停止操作
				end
				
			else
				
				return
			end
		end
		
		
		
	end
	
end


神秘随机移动坐标表= {
{643,336},
{547,360},
{552,410},
{643,432},
{711,414},
{736,371},
{690,332}
}

function 神秘()
	if arr["page1"]["神秘分次进"] == "true" and 多点找色(神秘地图, 0, 0) then
		local 神秘怪=0
		local 有怪=false
		多点找色(神秘地图, 1, 500)
		
		if 多点找色(小地图放大镜, 0, 0) then
			-- 随机选择一个坐标
			local 随机索引 = math.random(1, #神秘随机移动坐标表)
			local 随机坐标 = 神秘随机移动坐标表[随机索引]
			
			tap(随机坐标[1], 随机坐标[2])
			sleep(1000)
			多点找色(杀戮小地图叉, 1, 1000)
			
			
		end
		while true do
			sleep(200)
			
			local time = os.time()
			local hour = os.date("*t", time).hour
			local minute = os.date("*t", time).min
			
			-- 神秘活动时间：0-18点，每小时29-30分钟（支持18点30分最后一波）
			if hour <= 18 and (minute >= 29 and minute <= 30) then
				显示("在神秘")
				各类弹窗()
				--[===[if 卡屏检测()then
				return
				end]===]
				加速(20)
				if 多点找色神秘(神秘boss, 1, 200) then
					神秘怪=1
					for _ = 1, 10 do
						sleep(200)
						--战斗规避杀戮()
						
						if 通用进入战斗() then
							多点找色(只点拒绝, 1, 300)
							break
						elseif 神秘挑战观看() then
							break
						end
					end
				else
					有怪=false
				end
				if 神秘怪==1 then
					for _, 坐标 in ipairs(神秘移动坐标表) do
						加速(20)
						多点找色(神秘地图, 1, 300)
						
						if 多点找色(小地图放大镜, 0, 0) then
							tap(坐标[1], 坐标[2])
							sleep(300)
							多点找色(杀戮小地图叉, 1, 200)
						end
						for i=1,10 do
							sleep(200)
							if 多点找色神秘(神秘boss, 1, 200) then
								有怪=true
								for _ = 1, 10 do
									sleep(200)
									--战斗规避杀戮()
									
									if 通用进入战斗() then
										多点找色(只点拒绝, 1, 300)
										break
									elseif 神秘挑战观看() then
										break
									end
									
								end
								
							end
							
						end
						
					end
					if 有怪==false and not 多点找色(战斗场, 0, 0)then
						显示("退出神秘")
						return true
					end
				end
				
				
			else
				-- 不在神秘活动时间或已过时间，准备退出
				if not 多点找色(战斗场, 0, 0) then
					显示("退出神秘")
					return true
				end
			end
			
			
		end
	end
end

华山取消卡={599,535,679,582,"B96735-101010","-2|4|B56632-101010|-4|7|B26431-101010|-12|7|D2C0BF-101010|-9|1|FFFFFF-101010|-8|-3|FFFFFF-101010|-11|-6|FFFFFF-101010|-16|-7|FEFEFE-101010|-22|-8|FEFEFE-101010|-23|-4|FFFFFF-101010|-22|0|FFFFFF-101010|-23|4|FFFFFF-101010|-26|4|672B28-101010|-30|5|FFFFFF-101010|9|1|FFFFFF-101010|11|-7|FEFEFE-101010|9|11|B49795-101010|24|4|FEFEFE-101010"}
竞技场面板叉={1099,8,1178,76,"D37920-101010","-3|-3|D4791F-101010|-6|-7|D17820-101010|-8|-9|D0771F-101010|5|-9|D47920-101010|11|-6|FCD989-101010|11|-2|FCD989-101010|-1|14|FBD988-101010|-2|11|FBD988-101010|-2|8|FCD989-101010|-7|12|FBD886-101010|-10|7|D57A20-101010|-14|1|FCDA89-101010|-16|-2|FCDA89-101010|-16|-4|FCDA89-101010|-17|1|FBDA88-101010|-18|6|FBD785-101010|-8|12|FCDB86-101010"}
function 去华山()
	关闭清爽界面()
	if 多点找色(活动图标, 1, 2000)  then
		if 多点找色(活动叉, 0, 0) then
			tap(200,285)
			sleep(1000)
			tap(420,395)
			sleep(1000)
		end
		for i = 1, 10 do
			sleep(1000)
			多点找色(送我去华山, 1, 3000)
			if 多点找色(三三竞技面板, 0, 0) then
				显示("到达华山")
				return true
			end
			
		end
	end
	
	
end
function 华山()
	if arr["page1"]["华山"] == "true" then
		local 华山次数=0
		--local 战斗计数=true  -- 用于跟踪当前战斗是否已经计数
		if 去华山() then
			while true do
				sleep(500)
				--各类弹窗()
				--[===[ if 卡屏检测()then
				return
				end]===]
				local time = os.time()
				local t = os.date("*t", time)
				local hour = t.hour
				local minute = t.min
				if 多点找色(长安城地图,0,0)then
					--多点找色(世界之王确定,1,300)
					return true
				end
				if hour == 21 and (minute >= 5 and minute <= 50) then
					显示("在华山")
					
					if 多点找色(开始匹配, 1, 1000) then
						print("开始匹配")
						--战斗计数=true
					end
					if 多点找色(匹配剑, 1, 1000) then
						print("点击匹配")
					end
					
					if 多点找色(战斗场, 0, 0) and 多点找色(战场自动, 1, 1000) then
						华山次数 = 华山次数 + 1
						显示("华山次数" .. 华山次数)
						print(华山次数)
					end
					
					if (华山次数 >= 15 or minute >= 48) and not 多点找色(战斗场, 0, 0) then
						
						
						if 多点找色(三三竞技面板, 0, 0) then
							for i = 1, 2 do
								sleep(1000)
								多点找色(首胜奖励, 1, 1000)
								多点找色(十战奖励, 1, 1000)
								多点找色(五胜奖励, 1, 1000)
							end
							
							多点找色(三三叉, 1, 2000)
							多点找色(竞技场面板叉, 1, 2000)
							
						end
						
						if 多点找色(物品弹窗, 1, 200) then
							for i = 1, 10 do
								sleep(300)
								多点找色(物品弹窗, 1, 200)
							end
						end
						
						if not 多点找色(战斗场, 0, 0) then
							
							
							显示("退出华山")
							return true
						end
						
					end
				else
					if not 多点找色(战斗场, 0, 0) then
						
						return true
					end
				end
			end
		end
	end
end

local 上次闪退检查时间 = 0

function 闪退重启()
	--if arr["page1"]["闪退重启"]=="true" then
	
	-- 优化：只在30秒间隔后才检查闪退
	local 当前时间 = tickCount()
	if 当前时间 - 上次闪退检查时间 < 10000 then  -- 30秒间隔
		return
	end
	上次闪退检查时间 = 当前时间
	local pkg = "ccz.locojoy.mini.mt34.joysdk";
	local ret = appIsFront(pkg)--appIsRunning
	if ret == false then
		print("检测到游戏闪退，开始重启")
		runApp(pkg)
		for i=1,50 do
			sleep(1000)
			
			多点找色(登录,1,1000)
			多点找色(立即登录,1,1000)
			多点找色(立即登录二,1,1000)
			多点找色(关闭,1,1000)
			多点找色(开始游戏,1,1000)
			多点找色(开始游戏合区,1,1000)
			多点找色(只点拒绝,1,500)
			多点找色(奇缘拒绝,1,500)
			多点找色(奖励叉,1,1000)
			-- 移除递归调用各类弹窗()，避免无限递归
			if 多点找色(活动图标,0,0) or 多点找色(战斗场,0,0)or 多点找色(背包, 0, 0)then
				print("游戏重启成功")
				--坐骑()
				break
			end
			
			
		end
	end
	--end
end


local function get装备颜色()
	local 装备颜色 = {}
	
	if arr.page1["一阶"] == "true" then table.insert(装备颜色, 一阶装备) end
	if arr.page1["二阶"] == "true" then table.insert(装备颜色, 二阶装备) end
	if arr.page1["三阶"] == "true" then table.insert(装备颜色,三阶装备) end
	if arr.page1["四阶"] == "true" then table.insert(装备颜色, 四阶装备) end
	
	--[===[   if arr.page1["6000以上丹"] == "true" then table.insert(丹颜色, 六千以上丹) end]===]
	
	return 装备颜色
end

-- 修改丢丹啊函数
function 分解封装()
	if arr["page1"]["分解装备"] == "true" then
		if tonumber(os.time() - 分解时间) >= (tonumber(arr["page1"]["分解分"]) * 60) and not 多点找色(战斗场, 0, 0) then
			if 多点找色(背包, 1, 1000) then
				多点找色(一键整理, 1, 500)
				for h = 1, 4 do
					sleep(100)
					swipe(983, 256, 986, 530, 300) -- 向上滑
				end
				tap(851, 161)
				sleep(500)
			end
			
			if 多点找色(背包面板, 0, 0) then
				local 装备颜色 = get装备颜色() -- 使用动态获取的丹药颜色列表
				for i = 1, 10 do
					sleep(100)
					
					if 颜色检查(装备颜色, 1, 300) then
						多点找色(更多, 1, 400)
						多点找色(一键使用, 1, 400)
						多点找色(分解装备, 1, 400)
						多点找色(分解确定, 1, 500)
						多点找色(物品弹窗,1,400)
					end
				end
				
				if not 颜色检查(装备颜色, 0, 0)  then
					for i = 1, 4 do
						sleep(300)
						--print(i)
						if 多点找色(背包面板, 0, 0) then
							-- print("该滑动了")
							swipe(892, 577, 894, 300, 600)
							for j = 1, 10 do
								sleep(100)
								
								if 颜色检查(装备颜色, 1, 300) then
									多点找色(更多, 1, 400)
									多点找色(一键使用, 1, 400)
									多点找色(分解装备, 1, 400)
									多点找色(分解确定, 1, 500)
									多点找色(物品弹窗,1,400)
								end
							end
						end
					end
					
					多点找色(背包叉, 1, 1000)
					分解时间 = os.time()
					return
				end
			end
		end
	end
end

脚本开始函数()
