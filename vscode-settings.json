{
    // ==================== 字体和外观 ====================
    "editor.fontFamily": "'Fira Code', 'JetBrains Mono', 'Cascadia Code', 'Microsoft YaHei', 'PingFang SC', Consolas, monospace",
    "editor.fontLigatures": true,
    "editor.fontSize": 14,
    "editor.lineHeight": 1.6,
    "editor.fontWeight": "400",
    
    // ==================== 主题和颜色 ====================
    "workbench.colorTheme": "One Dark Pro",
    "workbench.iconTheme": "material-icon-theme",
    "workbench.preferredDarkColorTheme": "One Dark Pro",
    "workbench.preferredLightColorTheme": "Quiet Light",
    
    // ==================== 编辑器行为 ====================
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    "editor.detectIndentation": true,
    "editor.wordWrap": "on",
    "editor.wordWrapColumn": 120,
    "editor.rulers": [80, 120],
    
    // ==================== 视觉效果 ====================
    "editor.renderWhitespace": "boundary",
    "editor.renderIndentGuides": true,
    "editor.renderLineHighlight": "all",
    "editor.cursorBlinking": "smooth",
    "editor.cursorSmoothCaretAnimation": "on",
    "editor.smoothScrolling": true,
    
    // ==================== 行号和折叠 ====================
    "editor.lineNumbers": "on",
    "editor.folding": true,
    "editor.foldingStrategy": "indentation",
    "editor.showFoldingControls": "mouseover",
    "editor.foldingHighlight": true,
    
    // ==================== 括号和匹配 ====================
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": "active",
    "editor.matchBrackets": "always",
    "editor.autoClosingBrackets": "always",
    "editor.autoClosingQuotes": "always",
    
    // ==================== 选择和高亮 ====================
    "editor.selectionHighlight": true,
    "editor.occurrencesHighlight": true,
    "editor.wordHighlightBackground": "#575757",
    "editor.wordHighlightStrongBackground": "#004972",
    
    // ==================== 代码提示和补全 ====================
    "editor.suggestSelection": "first",
    "editor.acceptSuggestionOnCommitCharacter": false,
    "editor.acceptSuggestionOnEnter": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": false
    },
    
    // ==================== 格式化 ====================
    "editor.formatOnSave": true,
    "editor.formatOnType": false,
    "editor.formatOnPaste": true,
    
    // ==================== Lua特定设置 ====================
    "[lua]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": false,
        "editor.wordWrap": "on",
        "editor.rulers": [80, 120],
        "editor.defaultFormatter": "sumneko.lua"
    },
    
    // ==================== 文件关联 ====================
    "files.associations": {
        "*.lua": "lua",
        "*.luaej": "lua",
        "*.luacheckrc": "lua"
    },
    
    // ==================== Lua Language Server ====================
    "lua.diagnostics.globals": [
        "app", "sleep", "tap", "toast", "findMultiColor", "getPixelColor", 
        "swipe", "runApp", "appIsFront", "tickCount", "print", "math", "os", 
        "string", "table", "require", "arr", "pkgname", "显示", "多点找色", 
        "多点找色争霸", "多点找色世界", "多点找色阶", "多点找色星", "多点找色神秘",
        "多点找色通天", "多点找色重叠", "各类弹窗", "加速", "瞬移", "通用进入战斗",
        "争霸传送", "世界传送", "星传送", "杀戮爬楼", "华山", "神秘", "分解封装",
        "闪退重启", "争霸坐标表", "杀戮小地图坐标表", "神秘移动坐标表", 
        "挂星小地图坐标表", "世界勾选层数", "当前索引", "分解时间", "开始丢丹",
        "当前加速值", "上次加速更新时间", "怪物", "世界boss怪物", "星怪", 
        "杀戮战场阶", "神秘boss", "颜色检查", "卡地图挂星", "卡地图世界",
        "卡地图争霸", "卡地图杀戮", "挂星击杀够换图", "世界击杀满等"
    ],
    "lua.diagnostics.disable": [
        "lowercase-global",
        "undefined-global"
    ],
    "lua.completion.enable": true,
    "lua.hover.enable": true,
    "lua.signatureHelp.enable": true,
    
    // ==================== 工作区布局 ====================
    "workbench.sideBar.location": "left",
    "workbench.activityBar.visible": true,
    "workbench.statusBar.visible": true,
    "workbench.editor.showTabs": true,
    "workbench.editor.tabCloseButton": "right",
    "workbench.editor.tabSizing": "fit",
    
    // ==================== 终端设置 ====================
    "terminal.integrated.fontSize": 13,
    "terminal.integrated.fontFamily": "'Cascadia Code', 'Microsoft YaHei', monospace",
    "terminal.integrated.cursorBlinking": true,
    "terminal.integrated.cursorStyle": "line",
    
    // ==================== 文件资源管理器 ====================
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "explorer.compactFolders": false,
    
    // ==================== 搜索设置 ====================
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/tmp": true
    },
    
    // ==================== 性能优化 ====================
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/tmp/**": true
    },
    
    // ==================== 其他优化 ====================
    "workbench.list.smoothScrolling": true,
    "editor.minimap.enabled": true,
    "editor.minimap.side": "right",
    "editor.minimap.showSlider": "mouseover",
    "breadcrumbs.enabled": true,
    "problems.decorations.enabled": true,
    
    // ==================== 自动保存 ====================
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 2000,
    
    // ==================== Git设置 ====================
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true
}
