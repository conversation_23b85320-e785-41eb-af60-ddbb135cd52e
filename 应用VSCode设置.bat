@echo off
chcp 65001 >nul
echo ==========================================
echo          VSCode 设置优化脚本
echo ==========================================
echo.

echo 正在查找VSCode设置文件位置...

:: 查找VSCode用户设置文件
set "VSCODE_SETTINGS=%APPDATA%\Code\User\settings.json"

if exist "%VSCODE_SETTINGS%" (
    echo 找到VSCode设置文件: %VSCODE_SETTINGS%
    echo.
    
    :: 备份原设置文件
    echo 正在备份原设置文件...
    copy "%VSCODE_SETTINGS%" "%VSCODE_SETTINGS%.backup.%date:~0,4%%date:~5,2%%date:~8,2%" >nul
    echo 备份完成: %VSCODE_SETTINGS%.backup.%date:~0,4%%date:~5,2%%date:~8,2%
    echo.
    
    :: 复制新设置文件
    echo 正在应用新的设置...
    copy "优化的VSCode设置.json" "%VSCODE_SETTINGS%" >nul
    echo 设置应用完成！
    echo.
    
    echo ==========================================
    echo 设置已成功应用！请重启VSCode查看效果。
    echo.
    echo 主要改进：
    echo - 优化了字体和显示效果
    echo - 增强了Lua语言支持
    echo - 配置了中文显示
    echo - 优化了代码提示和高亮
    echo - 改善了整体界面体验
    echo ==========================================
    
) else (
    echo 错误：未找到VSCode设置文件
    echo 请确保VSCode已正确安装
    echo.
    echo 你也可以手动应用设置：
    echo 1. 打开VSCode
    echo 2. 按 Ctrl+Shift+P
    echo 3. 输入 "Preferences: Open Settings (JSON)"
    echo 4. 将 "优化的VSCode设置.json" 的内容复制进去
    echo 5. 保存文件
)

echo.
pause
