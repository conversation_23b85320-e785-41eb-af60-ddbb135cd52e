# VSCode 推荐插件列表

## 🎨 主题和美化插件

### 必装插件
- **One Dark Pro** - 最受欢迎的深色主题
- **Material Icon Theme** - 美观的文件图标
- **Bracket Pair Colorizer 2** - 彩色括号匹配
- **indent-rainbow** - 彩色缩进线

### 可选美化插件
- **Tokyo Night** - 温和的深色主题
- **Dracula Official** - 经典紫色主题
- **Better Comments** - 增强注释显示
- **Color Highlight** - 高亮颜色值

## 🔧 Lua开发插件

### 核心插件
- **Lua** (sumneko.lua) - Lua Language Server（已安装）
- **Lua Debug** - Lua调试器
- **Code Runner** - 一键运行代码

### 代码质量插件
- **Luacheck** - 静态代码分析（可选）
- **Lua Format** - 代码格式化

## 🚀 效率提升插件

### 编辑增强
- **Auto Rename Tag** - 自动重命名标签
- **Path Intellisense** - 路径智能提示
- **GitLens** - Git增强工具
- **Todo Tree** - TODO高亮和管理

### 文件管理
- **File Utils** - 文件操作工具
- **Project Manager** - 项目管理
- **Bookmarks** - 代码书签

## 📊 实用工具插件

### 数据处理
- **Rainbow CSV** - CSV文件彩色显示
- **Excel Viewer** - Excel文件查看器
- **JSON Tools** - JSON格式化工具

### 其他工具
- **Live Server** - 本地服务器（如果需要）
- **REST Client** - API测试工具
- **Thunder Client** - HTTP客户端

## 🎯 针对你的游戏脚本项目

### 特别推荐
1. **One Dark Pro** - 深色主题，长时间编码护眼
2. **Bracket Pair Colorizer 2** - 对复杂嵌套结构很有用
3. **indent-rainbow** - 清晰显示代码层级
4. **Better Comments** - 区分不同类型的注释
5. **Code Runner** - 快速测试代码片段

### 安装方式
1. 打开VSCode
2. 按 `Ctrl+Shift+X` 打开扩展面板
3. 搜索插件名称并安装
4. 重启VSCode使插件生效

## ⚙️ 使用配置文件

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Preferences: Open Settings (JSON)"
3. 将 `vscode-settings.json` 的内容复制到设置文件中
4. 保存并重启VSCode

## 🔥 快捷键推荐

- `Ctrl+Shift+P` - 命令面板
- `Ctrl+,` - 打开设置
- `Ctrl+K Ctrl+T` - 切换主题
- `Ctrl++/-` - 调整字体大小
- `Alt+Z` - 切换自动换行
- `Ctrl+Shift+\` - 跳转到匹配的括号
- `Ctrl+/` - 切换行注释
- `Shift+Alt+A` - 切换块注释
